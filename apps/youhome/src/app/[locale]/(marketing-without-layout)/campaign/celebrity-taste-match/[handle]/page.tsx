'use client';

import { notFound } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import { AnalysisLoading } from '../components/AnalysisLoading';
import { GrowthCard } from '../components/card/GrowthCard';
import { LaunchCard } from '../components/card/LaunchCard';
import { SoulFormulaCard } from '../components/card/SoulFormulaCard';
import { YoumindCard } from '../components/card/YoumindCard';
import { Footer } from '../components/footer';
import { DetailNavbar } from '../components/navbar/detailNavbar';
import { useAnalysisStatus } from '../hooks/useAnalysisStatus';
import { mockUser } from '../mock';
import {
  AIAnalysisResult,
  GrowthCardData,
  LaunchCardData,
  SoulFormulaData,
  UserData,
  UserInfo,
} from '../types';
import { StreamParser } from '../utils/stream-parser';

interface PageProps {
  params: Promise<{
    handle: string;
  }>;
}

export default function TwitterAnalysisPage({ params }: PageProps) {
  const [handle, setHandle] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [analysisResult, setAnalysisResult] = useState<{
    launchCardData?: LaunchCardData;
    growthCardData?: GrowthCardData;
    soulFormulaData?: SoulFormulaData;
  }>({});
  const [userDetails, setUserDetails] = useState(mockUser);
  const [error, setError] = useState<string | null>(null);

  // 使用 useRef 来跟踪初始化状态，避免重复执行
  const initRef = useRef(false);

  const { status, updateStepProgress, formatTime } = useAnalysisStatus();

  useEffect(() => {
    if (initRef.current) return;

    console.log('🔄 开始初始化分析...');
    initRef.current = true;

    const initAnalysis = async () => {
      try {
        const { handle: handleParam } = await params;
        setHandle(handleParam);

        updateStepProgress('tweets-fetching', '正在获取推文数据...', null);
        // 获取推文数据
        const response = await fetch(
          `/campaign/celebrity-taste-match/api/tweets?handle=${encodeURIComponent(handleParam)}`,
        );

        if (!response.ok) {
          throw new Error(`获取推文失败: ${response.status}`);
        }

        const userData = (await response.json()) as UserData;

        if (!userData.success) {
          throw new Error('获取推文失败');
        }

        const tweets = userData.data.tweets;
        updateStepProgress('tweets-fetching', '推文获取完成:', `获取到${tweets.length}条推文`);

        // 使用真实的用户信息进行流式分析
        const userInfo: UserInfo = {
          description: handleParam,
          tweets: tweets,
        };
        setUserDetails(userData.data.userDetails);

        const res = await fetch('/campaign/celebrity-taste-match/api/analysis', {
          method: 'POST',
          body: JSON.stringify({ userInfo }),
          headers: { 'Content-Type': 'application/json' },
        });

        if (!res.body) {
          throw new Error('No response body');
        }

        const reader = res.body.getReader();
        const decoder = new TextDecoder('utf-8');

        // 创建流式数据解析器
        const streamParser = new StreamParser({
          onProgress: (step, message, data) => {
            console.log(`📈 进度更新: ${step} - ${message}`, data);
            // 直接更新，不检查重复，因为流式数据可能包含相同步骤的不同消息
            updateStepProgress(step, message, data);
          },
          onComplete: () => {
            console.log('✅ 分析完成');
            // 更新状态为完成
            updateStepProgress('complete', '分析已完成！');
          },
          onError: (error) => {
            console.error('❌ 流式错误:', error);
            setError(`流式错误: ${error}`);
          },
          onAnalysisResult: (result: AIAnalysisResult) => {
            console.log('🎯 解析的AI分析结果:', result);

            // 更新Card数据
            setAnalysisResult({
              launchCardData: result.LaunchCard,
              growthCardData: result.GrowthCard,
              soulFormulaData: result.SoulFormula,
            });
          },
        });

        while (true) {
          const { value, done } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });

          // 使用按块解析，处理可能被分割的数据
          streamParser.parseChunk(chunk);
        }

        // 处理流结束时的剩余数据
        streamParser.flush();
      } catch (error) {
        console.error('分析失败:', error);
        setError(error instanceof Error ? error.message : '未知错误');
      } finally {
        console.log('✅ 分析初始化完成');
        setIsLoading(false);
      }
    };

    initAnalysis();
  }, [params]);

  // 如果还在加载，显示加载页面
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 px-4 py-12 sm:px-12 md:px-28 md:pt-24 to-purple-50">
        <div className="fixed top-0 left-0 right-0 z-50">
          <DetailNavbar />
        </div>
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-blue-300/10 to-purple-300/10 rounded-full blur-3xl animate-pulse"></div>
        </div>

        <AnalysisLoading status={status} formatTime={formatTime} />
      </div>
    );
  }

  // 如果有错误，显示错误页面
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="fixed top-0 left-0 right-0 z-50">
          <DetailNavbar />
        </div>
        <div className="flex-1 flex items-center justify-center pt-32">
          <div className="text-center max-w-md mx-auto px-4">
            <div className="bg-red-50 border border-red-200 rounded-xl p-6">
              <div className="text-red-600 text-lg font-semibold mb-2">分析失败</div>
              <p className="text-red-500">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 如果没有分析结果，显示404
  if (!analysisResult.soulFormulaData) {
    return notFound();
  }

  // 返回渲染结果页面
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 px-4 py-12 sm:px-12 md:px-28 md:pt-24 to-purple-50">
      <div className="fixed top-0 left-0 right-0 z-50">
        <DetailNavbar />
      </div>

      <div className="flex-1 flex flex-col pt-24 w-full mx-auto px-4">
        {/* 主标题区域 */}
        <div className="text-center mb-12 relative">
          {/* 装饰性背景元素 */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
            <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-tr from-purple-400/20 to-pink-400/20 rounded-full blur-3xl"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-gradient-to-r from-blue-300/10 to-purple-300/10 rounded-full blur-3xl"></div>
          </div>

          {/* 主标题 */}
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              名人匹配
            </span>
            <span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              结果
            </span>
          </h1>

          {/* 副标题 */}
          <p className="text-xl sm:text-2xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed">
            基于你的表达方式，AI为你匹配了相似品味的名人。
            <br className="hidden sm:block" />
            发现与你内心风格共鸣的"名人灵魂"。
          </p>

          {/* 装饰性分隔线 */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <div className="w-12 h-0.5 bg-gradient-to-r from-transparent to-blue-400"></div>
            <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
            <div className="w-12 h-0.5 bg-gradient-to-l from-transparent to-purple-400"></div>
          </div>
        </div>

        {/* 人格分析区域 */}
        <div className="border-t border-gray-100 pt-4 max-w-6xl mx-auto">
          <div className="mb-12">
            <div>
              <SoulFormulaCard analysisData={analysisResult.soulFormulaData!} user={userDetails} />
            </div>
          </div>

          {/* 其他card区域 */}
          <div className="flex flex-col gap-12 mb-6 mt-6">
            {analysisResult.launchCardData && (
              <LaunchCard data={analysisResult.launchCardData} user={userDetails} />
            )}
            {analysisResult.growthCardData && (
              <GrowthCard data={analysisResult.growthCardData} user={userDetails} />
            )}
          </div>

          {/* YouMind 卡片 */}
          <div className="flex flex-col items-center justify-center rounded-2xl p-8 text-white text-center mb-8">
            <YoumindCard />
          </div>

          <div className="w-full border-t border-gray-300 mt-8"></div>

          <div className="flex-1 flex items-center justify-center">
            <Footer />
          </div>
        </div>
      </div>
    </div>
  );
}
