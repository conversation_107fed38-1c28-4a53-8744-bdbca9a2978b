import type { StreamData } from '../lib/ai-analysis-service';
import type { AIAnalysisResult } from '../types';

export interface StreamParserCallbacks {
  onStatus?: (message: string) => void;
  onProgress?: (step: string, message: string, data?: any) => void;
  onPartialContent?: (content: string, accumulated: string) => void;
  onComplete?: (content: string) => void;
  onError?: (error: string) => void;
  onAnalysisResult?: (result: AIAnalysisResult) => void;
}

export class StreamParser {
  private callbacks: StreamParserCallbacks;
  private accumulatedContent = '';
  private buffer = ''; // 用于处理跨chunk的数据

  constructor(callbacks: StreamParserCallbacks) {
    this.callbacks = callbacks;
  }

  /**
   * 解析流式数据块
   * 处理可能被分割在多个chunk中的数据
   */
  parseChunk(chunk: string): void {
    // 将新数据添加到缓冲区
    this.buffer += chunk;

    // 按行分割，但保留最后一行（可能不完整）
    const lines = this.buffer.split('\n');
    const completeLines = lines.slice(0, -1); // 完整的行
    const incompleteLine = lines[lines.length - 1] || ''; // 可能不完整的最后一行

    // 处理完整的行
    for (const line of completeLines) {
      if (line.trim() && line.startsWith('data: ')) {
        this.parseStreamData(line);
      }
    }

    // 保留不完整的行到缓冲区
    this.buffer = incompleteLine;
  }

  /**
   * 解析完整的流式数据行
   */
  parseStreamData(data: string): void {
    if (!data.startsWith('data: ')) {
      return;
    }

    const jsonData = data.slice(6);

    try {
      const parsed: StreamData = JSON.parse(jsonData);

      switch (parsed.type) {
        case 'status':
          this.callbacks.onStatus?.(parsed.message || '');
          break;

        case 'progress':
          this.callbacks.onProgress?.(parsed.step || '', parsed.message || '', parsed.data);
          break;

        case 'partial_content':
          if (parsed.content && parsed.accumulated) {
            this.accumulatedContent = parsed.accumulated;
            this.callbacks.onPartialContent?.(parsed.content, parsed.accumulated);
          }
          break;

        case 'complete':
          if (parsed.content) {
            this.accumulatedContent = parsed.content;
            this.callbacks.onComplete?.(parsed.content);

            // 尝试解析AI分析结果
            this.tryParseAnalysisResult(parsed.content);
          }
          break;

        case 'error':
          this.callbacks.onError?.(parsed.error || '未知错误');
          break;

        default:
          console.warn('未知的流式数据类型:', parsed.type);
      }
    } catch (e) {
      console.warn('解析流数据失败:', e, '原始数据:', data);
    }
  }

  /**
   * 处理流结束，处理缓冲区中剩余的数据
   */
  flush(): void {
    if (this.buffer.trim()) {
      this.parseStreamData(this.buffer);
    }
  }

  /**
   * 尝试解析AI分析结果
   */
  private tryParseAnalysisResult(content: string): void {
    try {
      const cleanContent = this.extractJsonFromMarkdown(content);
      const analysisResult = JSON.parse(cleanContent) as AIAnalysisResult;

      console.log('🎯 解析的AI分析结果:', analysisResult);
      this.callbacks.onAnalysisResult?.(analysisResult);
    } catch (parseError) {
      console.error('解析AI分析结果失败:', parseError);
      this.callbacks.onError?.(
        `解析AI分析结果失败: ${parseError instanceof Error ? parseError.message : '未知错误'}`,
      );
    }
  }

  /**
   * 从 Markdown 格式的响应中提取 JSON 内容
   */
  private extractJsonFromMarkdown(content: string): string {
    // 移除 ```json 和 ``` 标记
    let cleanContent = content.replace(/```json\s*/g, '').replace(/```\s*$/g, '');

    // 如果内容仍然包含 Markdown 格式，尝试提取 JSON 对象
    if (cleanContent.includes('{') && cleanContent.includes('}')) {
      const jsonStart = cleanContent.indexOf('{');
      const jsonEnd = cleanContent.lastIndexOf('}') + 1;
      cleanContent = cleanContent.substring(jsonStart, jsonEnd);
    }

    // 清理多余的空白字符
    cleanContent = cleanContent.trim();

    console.log('analysis content:', cleanContent);
    return cleanContent;
  }

  /**
   * 获取累积的内容
   */
  getAccumulatedContent(): string {
    return this.accumulatedContent;
  }

  /**
   * 获取缓冲区内容（用于调试）
   */
  getBuffer(): string {
    return this.buffer;
  }
}
