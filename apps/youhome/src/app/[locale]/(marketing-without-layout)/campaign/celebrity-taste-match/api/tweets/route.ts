import { NextRequest } from 'next/server';
import { TwitterService } from '../../lib/twitter-service';
import { UserData } from '../../types';
import { transformPostsForAI } from '../../utils/twitter-transformer';
import { getTwitterHighQualityAvatar } from '../../utils/twitterAvatar';

export const runtime = 'edge'; // 添加 Edge Runtime 支持

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const handle = searchParams.get('handle');

  if (!handle) {
    return new Response(JSON.stringify({ error: 'Missing handle parameter' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  try {
    const twitterService = new TwitterService();
    const userTweets = await twitterService.getUserPost(handle);

    if (!userTweets.success) {
      throw new Error('获取推文失败');
    }

    // 从第一条推文中构建userDetails
    const firstTweet = userTweets.data.data?.[0];
    const userDetails = firstTweet
      ? {
          username: handle,
          display_name: firstTweet.user?.name || handle,
          profile_image_url: getTwitterHighQualityAvatar(firstTweet.user?.profile_pic_url),
          description: firstTweet.user?.description || '',
        }
      : {
          username: handle,
          display_name: handle,
          profile_image_url: getTwitterHighQualityAvatar(''),
          description: '',
        };

    const responseData: UserData = {
      success: true,
      data: {
        tweets: transformPostsForAI(userTweets.data.data || []),
        userDetails,
      },
    };

    return new Response(JSON.stringify(responseData), {
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('获取推文失败:', error);

    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : '获取推文失败',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      },
    );
  }
}
