import { NextRequest } from 'next/server';
import { AIAnalysisService } from '../../lib/ai-analysis-service'; // 你服务的路径
import { UserInfo } from '../../types';

export const runtime = 'edge'; // ⚠️ 可选：用 Edge Runtime 支持更快流式推送

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const userInfo: UserInfo = body.userInfo;

    const ai = new AIAnalysisService();

    // 获取流式分析的结果
    const stream = await ai.analyzeUserTweetsWithFieldAnalysisStream(userInfo);

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream', // SSE 流式格式
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
      },
    });
  } catch (error) {
    console.error('❌ 流式分析失败:', error);

    const errorMessage = error instanceof Error ? error.message : '未知错误';

    return new Response(`data: ${JSON.stringify({ error: errorMessage })}\n\n`, {
      status: 500,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
      },
    });
  }
}
