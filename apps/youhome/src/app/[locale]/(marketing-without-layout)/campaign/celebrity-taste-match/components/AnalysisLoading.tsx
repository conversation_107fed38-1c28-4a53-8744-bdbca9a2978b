import { Brain, Database, Sparkles, Users, Zap } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { AnalysisStatus } from '../types';

interface AnalysisLoadingProps {
  status: AnalysisStatus;
  formatTime: (seconds: number) => string;
}

export const AnalysisLoading = ({ status, formatTime }: AnalysisLoadingProps) => {
  // 暂时移除国际化，避免错误
  // const t = useTranslations('celebrity-taste-match');

  // 根据当前步骤获取对应的描述和图标
  const getStepInfo = (stepId: string) => {
    const currentStep = status.currentStep;

    switch (stepId) {
      case 'idle':
        return {
          title: '准备分析',
          description: status.message || '正在准备开始分析...',
          icon: Sparkles,
          color: 'blue',
          isActive: currentStep === 'idle',
          isCompleted: false,
        };
      case 'tweets-fetching':
        return {
          title: '获取推文数据',
          description: status.message || '正在从Twitter获取用户的最新推文...',
          icon: Users,
          color: 'blue',
          isActive: currentStep === 'tweets-fetching',
          isCompleted:
            currentStep !== 'tweets-fetching' &&
            ['field-analysis', 'database-loading', 'ai-analysis', 'complete'].includes(currentStep),
        };
      case 'field-analysis':
        return {
          title: '领域分析',
          description: status.message || '正在分析用户的兴趣领域...',
          icon: Brain,
          color: 'purple',
          isActive: currentStep === 'field-analysis',
          isCompleted:
            currentStep !== 'field-analysis' &&
            ['database-loading', 'ai-analysis', 'complete'].includes(currentStep),
        };
      case 'database-loading':
        return {
          title: '加载数据库',
          description: status.message || '正在加载相关领域的数据库...',
          icon: Database,
          color: 'green',
          isActive: currentStep === 'database-loading',
          isCompleted:
            currentStep !== 'database-loading' && ['ai-analysis', 'complete'].includes(currentStep),
        };
      case 'ai-analysis':
        return {
          title: 'AI分析',
          description: status.message || '正在生成个性化分析结果...',
          icon: Zap,
          color: 'orange',
          isActive: currentStep === 'ai-analysis',
          isCompleted: currentStep !== 'ai-analysis' && ['complete'].includes(currentStep),
        };
      case 'complete':
        return {
          title: '分析完成',
          description: '个性化分析已完成！',
          icon: Sparkles,
          color: 'emerald',
          isActive: currentStep === 'complete',
          isCompleted: currentStep === 'complete',
        };
      default:
        return {
          title: '处理中',
          description: status.message || '正在处理...',
          icon: Sparkles,
          color: 'blue',
          isActive: false,
          isCompleted: false,
        };
    }
  };

  const getColorClasses = (color: string, isActive: boolean, isCompleted: boolean) => {
    const baseClasses = 'rounded-xl border';
    if (isCompleted) {
      return `${baseClasses} bg-gradient-to-r from-emerald-50 to-emerald-100 border-emerald-200/50`;
    }
    if (isActive) {
      switch (color) {
        case 'blue':
          return `${baseClasses} bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200/50`;
        case 'purple':
          return `${baseClasses} bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200/50`;
        case 'green':
          return `${baseClasses} bg-gradient-to-r from-green-50 to-green-100 border-green-200/50`;
        case 'orange':
          return `${baseClasses} bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200/50`;
        case 'emerald':
          return `${baseClasses} bg-gradient-to-r from-emerald-50 to-emerald-100 border-emerald-200/50`;
        default:
          return `${baseClasses} bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200/50`;
      }
    }
    return `${baseClasses} bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200/50`;
  };

  const getTextColor = (color: string, isActive: boolean, isCompleted: boolean) => {
    if (isCompleted) return 'text-emerald-800';
    if (isActive) {
      switch (color) {
        case 'blue':
          return 'text-blue-800';
        case 'purple':
          return 'text-purple-800';
        case 'green':
          return 'text-green-800';
        case 'orange':
          return 'text-orange-800';
        case 'emerald':
          return 'text-emerald-800';
        default:
          return 'text-blue-800';
      }
    }
    return 'text-gray-600';
  };

  const getIconColor = (color: string, isActive: boolean, isCompleted: boolean) => {
    if (isCompleted) return 'text-emerald-600';
    if (isActive) {
      switch (color) {
        case 'blue':
          return 'text-blue-600';
        case 'purple':
          return 'text-purple-600';
        case 'green':
          return 'text-green-600';
        case 'orange':
          return 'text-orange-600';
        case 'emerald':
          return 'text-emerald-600';
        default:
          return 'text-blue-600';
      }
    }
    return 'text-gray-400';
  };

  const steps = [
    'tweets-fetching',
    'field-analysis',
    'database-loading',
    'ai-analysis',
    'complete',
  ];

  return (
    <div className="relative z-10 flex flex-col min-h-screen pt-20">
      <div className="flex-1 flex flex-col justify-center px-4 sm:px-6 lg:px-8 max-w-4xl mx-auto w-full">
        {/* Loading 区域 */}
        <div className="flex justify-center mb-16">
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 sm:p-12 shadow-2xl border border-white/50 max-w-2xl w-full">
            {/* 主要加载动画 */}
            <div className="text-center mb-12">
              {/* 多层旋转加载器 */}
              <div className="relative inline-block mb-8">
                <div className="w-24 h-24 sm:w-32 sm:h-32 relative">
                  {/* 外圈 */}
                  <div className="absolute inset-0 rounded-full border-4 border-blue-200"></div>
                  {/* 旋转的渐变圈 */}
                  <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-blue-600 border-r-purple-600 animate-spin"></div>
                  {/* 内圈装饰 */}
                  <div className="absolute inset-4 rounded-full bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
                    <Sparkles className="w-8 h-8 sm:w-10 sm:h-10 text-blue-600 animate-pulse" />
                  </div>
                </div>
              </div>

              {/* Loading 文字 */}
              <div className="space-y-4">
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-800 mb-2">
                  {status.currentStep === 'complete' ? '分析完成' : '正在分析...'}
                </h2>
                <p className="text-gray-600 text-base sm:text-lg max-w-md mx-auto leading-relaxed">
                  {status.message || '正在为您生成个性化的分析结果...'}
                </p>
              </div>
            </div>

            {/* 进度指示器 */}
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h3 className="text-lg font-semibold text-gray-700 mb-4">分析进度</h3>
              </div>

              {/* 进度步骤 */}
              <div className="space-y-4">
                {steps.map((stepId) => {
                  const stepInfo = getStepInfo(stepId);
                  const IconComponent = stepInfo.icon;
                  const isActive = stepInfo.isActive;
                  const isCompleted = stepInfo.isCompleted;

                  return (
                    <div
                      key={stepId}
                      className={`flex items-center justify-between p-4 ${getColorClasses(stepInfo.color, isActive, isCompleted)}`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          {isCompleted ? (
                            <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                          ) : isActive ? (
                            <>
                              <div
                                className={`w-3 h-3 bg-${stepInfo.color}-500 rounded-full animate-pulse`}
                              ></div>
                              <div
                                className={`absolute inset-0 w-3 h-3 bg-${stepInfo.color}-500 rounded-full animate-ping opacity-75`}
                              ></div>
                            </>
                          ) : (
                            <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                          )}
                        </div>
                        <div className="flex flex-col">
                          <span
                            className={`font-medium ${getTextColor(stepInfo.color, isActive, isCompleted)}`}
                          >
                            {stepInfo.title}
                          </span>
                          {isActive && (
                            <span className="text-xs text-gray-500 mt-1">
                              {stepInfo.description}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {isActive && (
                          <div className="flex space-x-1">
                            <div
                              className={`w-2 h-2 bg-${stepInfo.color}-500 rounded-full animate-bounce`}
                            ></div>
                            <div
                              className={`w-2 h-2 bg-${stepInfo.color}-500 rounded-full animate-bounce`}
                              style={{ animationDelay: '0.1s' }}
                            ></div>
                            <div
                              className={`w-2 h-2 bg-${stepInfo.color}-500 rounded-full animate-bounce`}
                              style={{ animationDelay: '0.2s' }}
                            ></div>
                          </div>
                        )}
                        <IconComponent
                          className={`w-5 h-5 ${getIconColor(stepInfo.color, isActive, isCompleted)} ${isActive ? 'animate-pulse' : ''}`}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* 提示信息 */}
              <div className="mt-8 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl border border-yellow-200/50">
                <div className="flex items-center space-x-2 text-yellow-800">
                  <Sparkles className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    {status.currentStep === 'complete'
                      ? '分析已完成，正在为您展示结果...'
                      : '请耐心等待，AI正在为您生成个性化分析...'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
