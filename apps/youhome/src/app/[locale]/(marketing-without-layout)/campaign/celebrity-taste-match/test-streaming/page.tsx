'use client';

import { useState } from 'react';
import { AnalysisLoading } from '../components/AnalysisLoading';
import { GrowthCard } from '../components/card/GrowthCard';
import { LaunchCard } from '../components/card/LaunchCard';
import { SoulFormulaCard } from '../components/card/SoulFormulaCard';
import { RealtimeDisplay } from '../components/testPageComponent/RealtimeDisplay';
import { useAnalysisStatus } from '../hooks/useAnalysisStatus';
import { mockUser } from '../mock';
import {
  AIAnalysisResult,
  GrowthCardData,
  LaunchCardData,
  SoulFormulaData,
  UserInfo,
} from '../types';
import { StreamParser } from '../utils/stream-parser';

export default function TestStreamingPage() {
  const [handle, setHandle] = useState('elemen_7');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState('');
  const [chunks, setChunks] = useState<string[]>([]);
  const [cardData, setCardData] = useState<{
    launchCardData?: LaunchCardData;
    growthCardData?: GrowthCardData;
    soulFormulaData?: SoulFormulaData;
  }>({});

  const { status, updateStepProgress, formatTime } = useAnalysisStatus();

  const testStreaming = async () => {
    setIsLoading(true);
    setResult('');
    setChunks([]);
    setCardData({});

    try {
      // 步骤2: 获取推文

      // 获取真实的推文数据
      const response = await fetch(
        `/campaign/celebrity-taste-match/api/tweets?handle=${encodeURIComponent(handle)}`,
      );

      if (!response.ok) {
        throw new Error(`获取推文失败: ${response.status}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || '获取推文失败');
      }

      const tweets = data.data.tweets;
      console.log(`📊 获取到 ${tweets.length} 条推文`);

      // 步骤3-5: 通过流式API处理所有步骤，状态由流式信息驱动

      // 使用真实的用户信息
      const userInfo: UserInfo = {
        description: handle,
        tweets: tweets,
      };

      const res = await fetch('/campaign/celebrity-taste-match/api/analysis', {
        method: 'POST',
        body: JSON.stringify({ userInfo }),
        headers: { 'Content-Type': 'application/json' },
      });

      if (!res.body) {
        throw new Error('No response body');
      }

      const reader = res.body.getReader();
      const decoder = new TextDecoder('utf-8');

      // 创建流式数据解析器
      const streamParser = new StreamParser({
        onStatus: (message) => {
          console.log('📊 状态更新:', message);
        },
        onProgress: (step, message, data) => {
          console.log(`📈 进度更新: ${step} - ${message}`, data);
          updateStepProgress(step, message, data);
        },
        onPartialContent: (content, accumulated) => {
          console.log('📝 部分内容:', content);
          console.log('📚 累积内容:', accumulated);
          setChunks((prev) => [...prev, content]);
        },
        onComplete: (content) => {
          console.log('✅ 分析完成');
          setResult(content);
          // 更新状态为完成
          updateStepProgress('complete', '分析已完成！');
        },
        onError: (error) => {
          console.error('❌ 流式错误:', error);
          setResult(`流式错误: ${error}`);
        },
        onAnalysisResult: (result: AIAnalysisResult) => {
          console.log('🎯 解析的AI分析结果:', result);

          // 更新Card数据
          setCardData({
            launchCardData: result.LaunchCard,
            growthCardData: result.GrowthCard,
            soulFormulaData: result.SoulFormula,
          });
        },
      });

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        console.log('🧩 Stream chunk:', chunk);

        // 使用按块解析，处理可能被分割的数据
        streamParser.parseChunk(chunk);
      }

      // 处理流结束时的剩余数据
      streamParser.flush();

      // 步骤6: 完成

      const finalContent = streamParser.getAccumulatedContent();
      setResult(finalContent);
      console.log('✅ 完整结果:', finalContent);

      // 直接使用流式数据块，不进行转换
      console.log('🎯 流式数据块数量:', chunks.length);
    } catch (error) {
      console.error('流式测试失败:', error);
      setResult(`错误: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">流式API测试</h1>

        {/* 状态显示 */}
        {isLoading && <AnalysisLoading status={status} formatTime={formatTime} />}

        {/* 控制面板 */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <div className="flex gap-4 items-center">
            <input
              type="text"
              value={handle}
              onChange={(e) => setHandle(e.target.value)}
              placeholder="输入Twitter用户名"
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={testStreaming}
              disabled={isLoading}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '测试中...' : '开始测试'}
            </button>
          </div>
        </div>

        {/* 解析后的Card显示 */}
        {cardData.soulFormulaData && (
          <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">解析后的Card数据</h2>
            <div className="space-y-6">
              <SoulFormulaCard analysisData={cardData.soulFormulaData} user={mockUser} />
              {cardData.launchCardData && (
                <LaunchCard data={cardData.launchCardData} user={mockUser} />
              )}
              {cardData.growthCardData && (
                <GrowthCard data={cardData.growthCardData} user={mockUser} />
              )}
            </div>
          </div>
        )}

        {/* 实时流式显示 */}
        <RealtimeDisplay chunks={chunks} accumulatedContent={result} isStreaming={isLoading} />

        {/* 完整结果显示 */}
        {result && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-800">完整结果</h2>
            <div className="bg-gray-50 p-4 rounded border">
              <pre className="text-sm text-gray-700 whitespace-pre-wrap overflow-x-auto">
                {result}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
