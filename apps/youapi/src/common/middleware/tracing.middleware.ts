/**
 * NestJS 追踪中间件
 * 从 youapp/src/middleware.ts 迁移而来的追踪逻辑
 * 适配到 NestJS Middleware 模式
 */

import { Injectable, type NestMiddleware } from '@nestjs/common';
import * as otel from '@opentelemetry/api';
import type { NextFunction, Request, Response } from 'express';
import { YouapiClsService } from '../services/cls.service';
import { type RichSpan } from '../tracing';

@Injectable()
export class TracingMiddleware implements NestMiddleware {
  constructor(private readonly clsService: YouapiClsService) {}

  use(_req: Request, _res: Response, next: NextFunction) {
    const span = otel.trace.getActiveSpan() as RichSpan | undefined;
    const traceId = span?.spanContext().traceId;
    if (traceId) {
      // 将 traceId 存储到 CLS 中，供后续服务使用
      this.clsService.setTraceId(traceId);
    }

    next();
  }
}
