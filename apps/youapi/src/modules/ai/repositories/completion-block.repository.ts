import { Injectable } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { DatabaseService } from '@/shared/db/database.service';
import { completionBlocks } from '@/shared/db/public.schema';
import { CompletionBlock, CompletionBlockDO } from '../domain/models/completion-block.entity';

@Injectable()
export class CompletionBlockRepository {
  constructor(private readonly databaseService: DatabaseService) {}

  async getById(id: string): Promise<CompletionBlock> {
    const result = await this.databaseService.db.query.completionBlocks.findFirst({
      where: eq(completionBlocks.id, id),
    });
    return CompletionBlock.fromDO(result as CompletionBlockDO);
  }

  async save(block: CompletionBlock): Promise<CompletionBlock> {
    if (block.isNew) {
      const blockData = block.toDO();
      const result = await this.databaseService.db
        .insert(completionBlocks)
        .values(blockData)
        .returning();
      return CompletionBlock.fromDO(result[0] as CompletionBlockDO);
    }

    await this.databaseService.db
      .update(completionBlocks)
      .set(block.toDO())
      .where(eq(completionBlocks.id, block.id));
    return block;
  }

  async saveMany(blocks: CompletionBlock[]): Promise<CompletionBlock[]> {
    console.log(`CompletionBlockRepository.saveMany called with ${blocks.length} blocks`);

    console.log('blocks', blocks);
    const newBlocks = blocks.filter((block) => block.isNew);
    console.log(`Found ${newBlocks.length} new blocks to insert`);

    if (newBlocks.length > 0) {
      const blockData = newBlocks.map((block) => block.toDO());
      console.log(
        'Inserting new blocks:',
        blockData.map((b) => ({ id: b.id, type: b.type, messageId: b.messageId })),
      );
      console.log('Full block data:', blockData);
      await this.databaseService.db.insert(completionBlocks).values(blockData);
      // Update the original blocks to mark them as no longer new
      newBlocks.forEach((block) => {
        block.markAsExisting();
      });
      console.log('New blocks inserted successfully');
    }

    const updatedBlocks = blocks.filter((block) => block.isModified);
    console.log(`Found ${updatedBlocks.length} modified blocks to update`);

    if (updatedBlocks.length > 0) {
      // Update each block individually since each has a unique ID
      for (const block of updatedBlocks) {
        const blockData = block.toDO();
        console.log(`Updating block ${block.id} of type ${block.type}`);
        await this.databaseService.db
          .update(completionBlocks)
          .set(blockData)
          .where(eq(completionBlocks.id, block.id));

        // Mark block as no longer modified
        block.markAsUnmodified();
      }
      console.log('Modified blocks updated successfully');
    }

    return blocks;
  }
}
