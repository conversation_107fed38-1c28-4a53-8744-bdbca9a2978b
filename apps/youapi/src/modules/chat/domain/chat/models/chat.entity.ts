/**
 * Chat Domain Entity - 聊天领域实体
 * 聊天的核心领域模型，负责聊天的业务逻辑
 *
 * Migrated from:
 * - youapp/lib/chat/domain/chat.entity.ts
 *
 */

import { AggregateRoot } from '@nestjs/cqrs';
import { CoreMessage } from 'ai';
import { uuidv7 } from 'uuidv7';
import {
  ChatBoardOrigin,
  ChatModeEnum,
  ChatOrigin,
  ChatOriginTypeEnum,
  ChatSnipOrigin,
  ChatThoughtOrigin,
  ChatWebpageOrigin,
  CompletionBlockTypeEnum,
  MessageRoleEnum,
  ToolNames,
} from '@/common/types';
import { camelToSnake, SafeParse } from '@/common/utils';
import { chats } from '@/dao/db/public.schema';
import { ToolCompletionBlock } from '../../../../ai/domain/models/completion-block.entity';
import {
  AssistantMessage,
  Message,
  MessageDO,
  UserMessage,
} from '../../message/models/message.entity';

export interface ChatLevelContext {
  content?: string;
  show_new_board_suggestion?: boolean;
}
export type ChatDO = typeof chats.$inferSelect & {
  mode: ChatModeEnum;
  originType: ChatOriginTypeEnum;
  messages?: MessageDO[];
};

export class Chat extends AggregateRoot {
  public readonly id: string;
  public readonly createdAt: Date;
  public updatedAt: Date;
  public deletedAt: Date | null;
  public readonly creatorId: string;
  public title: string | null;
  public readonly origin: ChatOrigin;
  public boardId: string | null;
  public readonly mode: ChatModeEnum;
  public webpageContent: string | null;
  public showNewBoardSuggestion: boolean;
  public messages: Message[] | null = null;

  public isModified = false;

  constructor(
    data: {
      id: string;
      createdAt: Date;
      updatedAt: Date;
      deletedAt: Date | null;
      creatorId: string;
      mode: ChatModeEnum;
      title: string | null;
      origin: ChatOrigin;
      boardId: string | null;
      webpageContent?: string;
      showNewBoardSuggestion?: boolean;
    },
    public readonly isNew: boolean = false,
  ) {
    super();
    this.id = data.id;
    this.createdAt = data.createdAt;
    this.updatedAt = data.updatedAt;
    this.deletedAt = data.deletedAt;
    this.creatorId = data.creatorId;
    this.mode = data.mode;
    this.title = data.title;
    this.origin = data.origin;
    this.boardId = data.boardId;
    this.webpageContent = data.webpageContent;
    this.showNewBoardSuggestion = data.showNewBoardSuggestion || false;
  }

  /**
   * 创建聊天
   * 创建一个新的聊天实例
   */
  static createNew(param: {
    creatorId: string;
    origin: ChatOrigin;
    title?: string;
    webpageContent?: string;
    showNewBoardSuggestion?: boolean;
    boardId?: string;
    mode?: ChatModeEnum;
  }): Chat {
    const now = new Date();

    return new Chat(
      {
        id: uuidv7(),
        createdAt: now,
        updatedAt: now,
        deletedAt: null,
        creatorId: param.creatorId,
        title: param.title || 'New chat',
        origin: param.origin || { type: ChatOriginTypeEnum.UNKNOWN },
        boardId:
          param.boardId || param.origin.type === ChatOriginTypeEnum.BOARD
            ? (param.origin as ChatBoardOrigin).id
            : null,
        mode: param.mode || ChatModeEnum.CHAT,
        webpageContent: param.webpageContent || null,
        showNewBoardSuggestion: param.showNewBoardSuggestion || false,
      },
      true,
    );
  }

  /**
   * 从数据创建聊天实体
   * 从数据库数据还原聊天实体
   */
  static fromDO(data: ChatDO): Chat {
    const origin: ChatOrigin = {
      type: data.originType,
      id: data.originId || undefined,
      url: data.originUrl || undefined,
    };
    const context: ChatLevelContext = SafeParse(data.context, true, {});

    return new Chat({
      id: data.id,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      deletedAt: data.deletedAt,
      creatorId: data.creatorId,
      mode: data.mode,
      title: data.title,
      origin: origin,
      boardId: data.boardId,
      webpageContent: context.content || null,
      showNewBoardSuggestion: context.show_new_board_suggestion || false,
    });
  }

  toDO(): ChatDO {
    return {
      id: this.id,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      deletedAt: this.deletedAt,
      creatorId: this.creatorId,
      mode: this.mode,
      title: this.title,
      originId: (this.origin as ChatBoardOrigin).id,
      originType: this.origin.type,
      originUrl: (this.origin as ChatWebpageOrigin).url,
      boardId: this.boardId,
      context: JSON.stringify({
        content: this.webpageContent,
        show_new_board_suggestion: this.showNewBoardSuggestion,
      }),
      // @deprecated
      essence: null,
      essenceLastUpdatedAt: null,
      essenceTraceId: null,
      assistantId: null,
    };
  }

  /**
   * 获取 Board ID
   */
  getBoardId(): string | null {
    const lastUserMessage = this.getLastUserMessage();
    if (lastUserMessage?.boardId) {
      return lastUserMessage.boardId;
    }
    if (this.boardId) {
      return this.boardId;
    }
    if (this.isNewBoardChat()) {
      return (this.origin as ChatBoardOrigin)?.id;
    }
    return null;
  }

  /**
   * 检查聊天是否在 New Board 工作流中
   */
  isInsideNewBoardWorkflow(): boolean {
    const lastAssistantMessage = this.getLastAssistantMessage() as AssistantMessage;
    const blocks = lastAssistantMessage?.blocks || [];
    return blocks.some(
      (b) =>
        b.type === CompletionBlockTypeEnum.TOOL &&
        (b as ToolCompletionBlock).toolName === ToolNames.CREATE_BOARD,
    );
  }

  /**
   * 更新聊天标题
   * 修改聊天的标题内容
   */
  updateTitle(title: string): void {
    if (this.isDeleted()) {
      throw new Error('Cannot update title of deleted chat');
    }

    this.title = title;
    this.updatedAt = new Date();
    this.isModified = true;
  }

  /**
   * 更新 Origin
   */
  updateOrigin(origin: ChatOrigin): void {
    if (this.isDeleted()) {
      throw new Error('Cannot update origin of deleted chat');
    }

    this.updatedAt = new Date();
    this.origin.type = origin.type;
    if (origin.type === ChatOriginTypeEnum.BOARD) {
      (this.origin as ChatBoardOrigin).id = origin.id;
      this.boardId = origin.id;
    } else if (origin.type === ChatOriginTypeEnum.WEBPAGE) {
      (this.origin as ChatWebpageOrigin).url = origin.url;
    } else if (origin.type === ChatOriginTypeEnum.SNIP) {
      (this.origin as ChatSnipOrigin).id = origin.id;
    }

    this.isModified = true;
  }

  /**
   * 删除聊天
   * 软删除聊天记录
   */
  delete(): void {
    if (this.isDeleted()) {
      throw new Error('Chat is already deleted');
    }

    this.deletedAt = new Date();
    this.updatedAt = new Date();
  }

  /**
   * 提交聊天状态，标记为已持久化
   */
  commit(): void {
    (this as any).isNew = false;
    this.isModified = false;
  }

  /**
   * 检查聊天是否已删除
   */
  isDeleted(): boolean {
    return this.deletedAt !== null;
  }

  /**
   * 检查聊天是否属于某用户
   */
  isOwnedBy(userId: string): boolean {
    return this.creatorId === userId;
  }

  /**
   * 检查聊天是否为某种模式
   */
  isMode(mode: ChatModeEnum): boolean {
    return this.mode === mode;
  }

  /**
   * 检查聊天是否来自某个源
   */
  isFromOrigin(origin: ChatOrigin): boolean {
    if (this.origin.type !== origin.type) {
      return false;
    }

    switch (origin.type) {
      case ChatOriginTypeEnum.WEBPAGE:
        return (this.origin as ChatWebpageOrigin).url === origin.url;
      case ChatOriginTypeEnum.SNIP:
      case ChatOriginTypeEnum.BOARD:
      case ChatOriginTypeEnum.THOUGHT:
        return (this.origin as ChatThoughtOrigin).id === origin.id;
      case ChatOriginTypeEnum.UNKNOWN:
        return true;
      default:
        return false;
    }
  }

  /**
   * 检查聊天是否属于某个板块
   */
  isInBoard(boardId: string): boolean {
    return this.getBoardId() === boardId;
  }

  /**
   * 检查聊天是否为新建板块聊天
   * 用于创建新板块的特殊聊天模式
   */
  isNewBoardChat(): boolean {
    return this.mode === ChatModeEnum.NEW_BOARD;
  }

  /**
   * 检查聊天是否为自定义助手聊天
   * @deprecated mode 已废弃
   */
  isCustomAssistantChat(): boolean {
    return this.mode === ChatModeEnum.CUSTOM_ASSISTANT;
  }

  /**
   * 检查聊天是否为助手预览聊天
   * @deprecated mode 已废弃
   */
  isAssistantPreviewChat(): boolean {
    return this.mode === ChatModeEnum.ASSISTANT_PREVIEW;
  }

  /**
   * 检查聊天是否为普通聊天
   */
  isNormalChat(): boolean {
    return this.mode === ChatModeEnum.CHAT;
  }

  getAllUserMessages(): UserMessage[] {
    if (!this.messages) {
      return [];
    }
    return this.messages.filter(
      (m) => m.role === MessageRoleEnum.USER && !m.isDeleted(),
    ) as UserMessage[];
  }

  /**
   * 获取最后一条用户消息
   */
  getLastUserMessage(): UserMessage | null {
    if (!this.messages) {
      return null;
    }
    return (this.messages.find(
      (message) => message.role === MessageRoleEnum.USER && !message.isDeleted(),
    ) || null) as UserMessage;
  }

  /**
   * 获取最后一条助手消息
   */
  getLastAssistantMessage(): AssistantMessage | null {
    if (!this.messages) {
      return null;
    }
    return (this.messages.find(
      (message) => message.role === MessageRoleEnum.ASSISTANT && !message.isDeleted(),
    ) || null) as AssistantMessage;
  }

  /**
   * 添加消息到聊天
   * 通用的消息添加方法，支持用户消息和助手消息
   */
  addMessage(message: Message) {
    if (!this.messages) {
      this.messages = [];
    }
    this.messages.push(message);
  }

  /**
   * 获取插件 Chat 上下文
   */
  getWebpageChatContext() {
    if (this.origin.type !== ChatOriginTypeEnum.WEBPAGE) {
      return null;
    }

    const { title, url, description } = this.origin;
    const content = this.webpageContent;
    return {
      description: description || '',
      content: content || '',
      title: title || '',
      url: url || '',
    };
  }

  /**
   * 获取 trace 上下文
   * @returns
   */
  getTraceMetadata() {
    return {
      chatId: this.id,
      messageId: this.getLastAssistantMessage()?.id,
      ...(this.origin?.type === ChatOriginTypeEnum.BOARD && {
        boardId: (this.origin as ChatBoardOrigin)?.id,
      }),
      ...(this.origin?.type === ChatOriginTypeEnum.SNIP && {
        snipId: (this.origin as ChatSnipOrigin)?.id,
      }),
      ...(this.origin?.type === ChatOriginTypeEnum.THOUGHT && {
        thoughtId: (this.origin as ChatThoughtOrigin)?.id,
      }),
      ...(this.origin?.type === ChatOriginTypeEnum.WEBPAGE && {
        url: (this.origin as ChatWebpageOrigin)?.url,
      }),
    };
  }

  /**
   * 转换为 CompletionStreamChunk
   * 用于消息流发送
   */
  toCompletionStreamChunk() {
    return camelToSnake({
      id: this.id,
      creatorId: this.creatorId,
      createdAt: this.createdAt.toISOString(),
      updatedAt: this.updatedAt.toISOString(),
      mode: this.mode,
      title: this.title,
      origin: this.origin,
      boardId: this.boardId,
    });
  }

  /**
   * Generation 表新建前的临时方案
   * @returns
   */
  toCoreMessages(): CoreMessage[] {
    return this.messages.flatMap((message) => message.toCoreMessages());
  }
}
