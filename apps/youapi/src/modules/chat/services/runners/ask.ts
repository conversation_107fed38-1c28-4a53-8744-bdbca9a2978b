import { QueryBus } from '@nestjs/cqrs';
import { CoreMessage } from 'ai';
import { ChatCompletionToolChoiceOption } from 'openai/resources/index.js';
import { ChatOriginTypeEnum, ToolNames } from '@/common/types';
import { ApplicationContext } from '@/common/utils/application-context';
import { Generation } from '@/modules/ai/domain/models/generation.entity';
import { GetBoardDirectoryStructureQuery } from '@/modules/material-mng';
import { Chat } from '../../domain/chat/models/chat.entity';
import { SendMessageCommand } from '../commands/send-message.command';
import { ChatRunner } from './chat';

export class Ask<PERSON>unner extends ChatRunner {
  protected readonly prompt = 'ai-ask-chat-prompt';
  protected readonly traceName = 'chat-assistant-ask';
  protected readonly queryBus: QueryBus;

  constructor(chat: Chat, userId: string) {
    super(chat, userId);
    this.queryBus = ApplicationContext.getProvider<QueryBus>(QueryBus);
  }

  protected async getPromptVariables(): Promise<Record<string, any>> {
    const aiLanguage = await this.userDomainService.getPrimaryResponseLanguage({
      user_id: this.userId,
    });
    const boardId = this.chat.getBoardId();
    const spaceId = this.youapiClsService.getSpaceId();
    const { directoryStructure, name } = await this.queryBus.execute(
      new GetBoardDirectoryStructureQuery(this.userId, spaceId, boardId),
    );

    return {
      language: aiLanguage,
      // boardName: 'Unsorted',
      // boardDirectoryStructure: 'No directory structure',
      boardName: name || 'Unsorted',
      boardDirectoryStructure:
        JSON.stringify(directoryStructure, null, 2) || 'No directory structure',
    };
  }

  protected async preparePromptMessages(): Promise<CoreMessage[]> {
    // 把上一轮的消息返回
    if (this.currentGeneration) {
      return [
        ...this.currentGeneration.promptMessages,
        ...this.currentGeneration.generatedMessages,
      ];
    }

    // 首次生成消息
    const { promptMessages } = await this.promptService.getPromptAndMessages(
      this.prompt,
      await this.getPromptVariables(),
    );
    return [...promptMessages, ...this.chat.toCoreMessages()];
  }

  protected getToolNames(command: SendMessageCommand): ToolNames[] {
    const tools: ToolNames[] = [];
    if (this.chat.origin?.type !== ChatOriginTypeEnum.WEBPAGE) {
      tools.push(ToolNames.GOOGLE_SEARCH);
      tools.push(ToolNames.BOARD_SEARCH);
    } else if (command.param.tools?.[ToolNames.GOOGLE_SEARCH]?.useTool === 'auto') {
      tools.push(ToolNames.GOOGLE_SEARCH);
    }

    return tools;
  }

  protected async setupGeneration(command: SendMessageCommand) {
    const assistantMessage = this.chat.getLastAssistantMessage();
    const promptMessages = await this.preparePromptMessages();

    const generation = new Generation({
      model: assistantMessage.model,
      // TODO: 测试代码，硬编码 tool 调用
      ...(!this.currentGeneration
        ? {
            tools: [ToolNames.GOOGLE_SEARCH],
            toolChoice: 'required' as ChatCompletionToolChoiceOption,
          }
        : {
            tools: this.getToolNames(command),
            toolChoice: 'auto' as ChatCompletionToolChoiceOption,
          }),
      prompt: this.promptService.fetchPrompt({ name: this.prompt }),
      promptMessages,
      traceMetadata: this.chat.getTraceMetadata(),
      bizArgs: this.currentGeneration?.bizArgs || {},
    });
    this.generations.push(generation);
    this.currentGeneration = generation;
  }
}
