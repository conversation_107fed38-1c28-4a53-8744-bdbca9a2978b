import { CoreMessage, LanguageModelV1, streamText, TextStreamPart } from 'ai';
import { catchError, EMPTY, merge, Observable, ReplaySubject, Subject, tap } from 'rxjs';
import z from 'zod';
import { RestError } from '@/common/errors';
import { YouapiClsService } from '@/common/services/cls.service';
import {
  CompletionStreamChunk,
  CompletionStreamModeEnum,
  GenerationStatusEnum,
  MessageStatusEnum,
  MODEL_DEFINITION,
  ToolNames,
} from '@/common/types';
import { ApplicationContext } from '@/common/utils/application-context';
import { UserDomainService } from '@/domain/user';
import { CallToolCommand } from '@/modules/ai/domain/commands/call-tool.command';
import { Generation } from '@/modules/ai/domain/models/generation.entity';
import { PromptService } from '@/modules/ai/prompt/index.service';
import { ModelProviderService } from '@/modules/ai/providers/index.service';
import { BaseRunner } from '@/modules/ai/runners/base';
import { streamToObservable } from '@/modules/ai/utils/toObservable';
import { UserRepository } from '@/modules/iam/repositories/user.repository';
import { Chat } from '../../domain/chat/models/chat.entity';
import { ChatRepository } from '../../repositories/chat.repository';
import { MessageRepository } from '../../repositories/message.repository';
import { SendMessageCommand } from '../commands/send-message.command';

export class ChatRunner extends BaseRunner {
  protected readonly traceName: string;
  protected readonly promptService: PromptService;
  protected readonly userRepository: UserRepository;
  protected readonly userDomainService: UserDomainService;
  protected readonly chatRepository: ChatRepository;
  protected readonly messageRepository: MessageRepository;
  protected readonly youapiClsService: YouapiClsService;
  protected generations: Generation[] = [];

  constructor(
    protected readonly chat: Chat,
    protected readonly userId: string,
  ) {
    super();
    this.promptService = ApplicationContext.getProvider<PromptService>(PromptService);
    this.userRepository = ApplicationContext.getProvider<UserRepository>(UserRepository);
    this.userDomainService = ApplicationContext.getProvider<UserDomainService>(UserDomainService);
    this.chatRepository = ApplicationContext.getProvider<ChatRepository>(ChatRepository);
    this.messageRepository = ApplicationContext.getProvider<MessageRepository>(MessageRepository);
    this.youapiClsService = ApplicationContext.getProvider<YouapiClsService>(YouapiClsService);
  }

  protected getGeneratedCoreMessages(): CoreMessage[] {
    return this.generations.flatMap((generation) => generation.generatedMessages);
  }

  protected assembleApiParameters(model: LanguageModelV1): Parameters<typeof streamText>[0] {
    const params = super.assembleApiParameters(model);
    const messages = this.promptMessages;

    // 加上 anthropic cacheControl
    params.messages = [
      ...messages.slice(0, -1),
      {
        ...messages[messages.length - 1],
        providerOptions: {
          anthropic: {
            cacheControl: { type: 'ephemeral' },
          },
        },
      },
    ];

    // TODO: 测试代码，后续替换为实际的 tools
    params.tools = {
      [ToolNames.GOOGLE_SEARCH]: {
        parameters: z.object({
          query: z.string().describe('Tool input parameters'),
        }),
        description: `Execute ${ToolNames.GOOGLE_SEARCH} tool`,
      },
    };
    params.toolChoice =
      typeof this.currentGeneration.toolChoice === 'string'
        ? this.currentGeneration.toolChoice
        : {
            type: 'tool',
            toolName: this.currentGeneration.toolChoice.function.name,
          };

    return params;
  }

  /**
   * 调用 streamText 并将 AI SDK 文本流转为 CompletionStreamChunk 流
   * 这里仅存放跟 Generation/CompletionBlock 的调用逻辑
   * Generation/CompletionBlock 内部实现应放置 AI 模块，避免与 Chat 业务逻辑耦合
   */
  protected async streamText<T>(
    subject: Subject<CompletionStreamChunk<T>>,
    command: SendMessageCommand,
  ) {
    const model = ApplicationContext.getProvider<ModelProviderService>(
      ModelProviderService,
    ).getLanguageModel(this.model, { useCache: false });
    const traceNode = await this.startTrace(model, this.traceName, {
      inheritTrace: true,
    });

    let onFirstChunk = false;
    const params = this.assembleApiParameters(model);

    // fake reasoning 控制流
    const controlChannel = new ReplaySubject<TextStreamPart<any>>();
    const modelDefinition = MODEL_DEFINITION.get(this.model);
    if (modelDefinition.extra?.fake_reasoning) {
      controlChannel.next({
        type: 'reasoning',
        textDelta: '',
      });
    }

    const generator = streamText({
      ...params,
      toolCallStreaming: true,
      onChunk: () => {
        if (onFirstChunk) return;

        onFirstChunk = true;
        traceNode.update({
          completionStartTime: new Date(),
        });
      },
      onFinish: async (result) => {
        const { usage, finishReason } = result;

        if (finishReason === 'tool-calls') {
          const toolCalls = result.toolCalls;
          traceNode.span({
            name: 'tool-calls',
            input: result.toolCalls,
          });

          for (const toolCall of toolCalls) {
            const toolMessage = await this.commandBus.execute(
              new CallToolCommand({
                traceNode, // CommandBus 在另一个线程上，AsyncLocalStorage 无法跨线程
                subject, // 仅限透传消息，其他消息通过 processTextStream 转换
                input: toolCall.args,
                generation: this.currentGeneration,
                completionBlock: this.currentGeneration.getToolBlockByToolId(toolCall.toolCallId),
              }),
            );

            // data: {"type":"tool-result","toolCallId":"call_EAK9uJQjgKCfaIVYa6UnplHJ","toolName":"googleSearch","args":{"query":"Cognition 公司"},"result":{"type":"text","text":"test"}}
            controlChannel.next({
              type: 'tool-result',
              toolCallId: toolCall.toolCallId,
              toolName: toolCall.toolName,
              args: toolCall.args,
              result: toolMessage.content[0].result,
              jsonResult: (toolMessage.content[0] as any).jsonResult,
            } as TextStreamPart<any> & { jsonResult: any });
          }

          traceNode.end({
            metadata: {
              success: true,
              finishReason,
            },
          });
          controlChannel.complete();
          this.currentGeneration.setStatus(GenerationStatusEnum.SUCCESS);
          await this.setupGeneration(command);
          // Set messageId for the new generation created in setupGeneration
          this.currentGeneration
            .setChatMessageId(this.chat.getLastAssistantMessage().id)
            .setBizArgs({
              chatId: this.chat.id,
              userId: this.userId,
              messageId: this.chat.getLastAssistantMessage().id,
            });
          await this.streamText(subject, command);
          return;
        }

        subject.complete();
        this.currentGeneration.setStatus(GenerationStatusEnum.SUCCESS);

        const usageReport = this.buildUsageReport(usage);
        traceNode.end({
          ...usageReport,
          metadata: {
            success: true,
            finishReason,
          },
        });
      },
      onError: ({ error }) => {
        subject.error(error);
        this.currentGeneration.setStatus(GenerationStatusEnum.FAILED);

        traceNode.end({
          metadata: {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            errorStack: error instanceof Error ? error.stack : undefined,
          },
        });
      },
    });

    const streamTextObservable = streamToObservable(generator);
    this.currentGeneration.processTextStream(
      merge(controlChannel, streamTextObservable),
      subject,
      traceNode,
    );
  }

  protected async setupGeneration(command: SendMessageCommand) {
    throw new Error('Not implemented');
  }

  /**
   * Chat Assistant 生成消息流入口
   * 处理跟 Message 相关的业务逻辑与消息发送（例如 Message 的新建、更新状态、更新字段等）
   */
  async generate<T>(command: SendMessageCommand): Promise<Observable<CompletionStreamChunk<any>>> {
    if (!this.chat.getLastAssistantMessage()) {
      throw new Error('Chat has no assistant message');
    }
    if (!this.chat.getLastUserMessage()) {
      throw new Error('Chat has no user message');
    }

    const messageSubject = new ReplaySubject<CompletionStreamChunk<any>>();
    messageSubject.next({
      mode: CompletionStreamModeEnum.INSERT,
      dataType: 'Chat',
      data: this.chat.toCompletionStreamChunk(),
    });

    console.log('save user message:', this.chat.getLastUserMessage().isNew);
    if (this.chat.getLastUserMessage().isNew) {
      console.log('Saving new user message:', this.chat.getLastUserMessage().id);
      await this.messageRepository.save(this.chat.getLastUserMessage());
      this.chat.getLastUserMessage().commit();
      console.log('User message saved successfully');
    }
    messageSubject.next({
      mode: CompletionStreamModeEnum.INSERT,
      dataType: 'Message',
      data: this.chat.getLastUserMessage().toCompletionStreamChunk(),
    });

    console.log('save assistant message:', this.chat.getLastAssistantMessage().isNew);
    if (this.chat.getLastAssistantMessage().isNew) {
      console.log('Saving new assistant message:', this.chat.getLastAssistantMessage().id);
      await this.messageRepository.save(this.chat.getLastAssistantMessage());
      this.chat.getLastAssistantMessage().commit();
      console.log('Assistant message saved successfully');
    }
    const assistantMessage = this.chat.getLastAssistantMessage();
    messageSubject.next({
      mode: CompletionStreamModeEnum.INSERT,
      dataType: 'Message',
      data: assistantMessage.toCompletionStreamChunk(),
    });
    messageSubject.next({
      mode: CompletionStreamModeEnum.REPLACE,
      targetType: 'Message',
      targetId: assistantMessage.id,
      path: 'trace_id',
      data: assistantMessage.traceId,
    });

    // Generation 生成是唯一逻辑不一样的地方
    await this.setupGeneration(command);
    this.currentGeneration.setChatMessageId(assistantMessage.id).setBizArgs({
      chatId: this.chat.id,
      userId: this.userId,
      messageId: assistantMessage.id,
    });

    // 开始生成消息流
    const streamSubject = new ReplaySubject<CompletionStreamChunk<any>>();
    await this.streamText<T>(streamSubject, command);

    const updateStatus = async (status: MessageStatusEnum) => {
      messageSubject.next({
        mode: CompletionStreamModeEnum.REPLACE,
        targetType: 'Message',
        targetId: assistantMessage.id,
        path: 'status',
        data: status,
      });
      assistantMessage.updateStatus(status);
      await this.messageRepository.save(assistantMessage);
      assistantMessage.commit();

      if (
        [MessageStatusEnum.DONE, MessageStatusEnum.ERROR, MessageStatusEnum.ABORT].includes(status)
      ) {
        messageSubject.complete();
      }
    };

    // 订阅 streamSubject 来处理完成和错误
    streamSubject
      .asObservable()
      .pipe(
        tap({
          complete: async () => {
            await updateStatus(MessageStatusEnum.DONE);
          },
        }),
        catchError(async (error) => {
          // 在停止之前发送错误作为 CompletionStreamChunk
          const errorChunk: CompletionStreamChunk<any> = {
            mode: CompletionStreamModeEnum.REPLACE,
            targetType: 'Message',
            targetId: assistantMessage.id,
            path: 'error',
            data:
              error instanceof RestError
                ? error.json('')
                : {
                    code: (error as Error).name,
                    status: 400,
                    message: (error as Error).message,
                  },
          };

          messageSubject.next(errorChunk);
          assistantMessage.setError(error);
          await updateStatus(MessageStatusEnum.ERROR);
          return EMPTY;
        }),
      )
      .subscribe(); // 订阅以确保 tap 和 catchError 被执行

    return merge(messageSubject.asObservable(), streamSubject.asObservable());
  }
}
