import { IQueryHandler, QueryHandler } from '@nestjs/cqrs';
import { BoardDto } from '../../../dto/board.dto';
import { BoardRepository } from '../../../repositories/board.repository';
import { BoardDtoService } from '../../dto-services/board-dto.service';
import { GetBoardQuery } from '../../queries/board/get-board.query';

@QueryHandler(GetBoardQuery)
export class GetBoardHandler implements IQueryHandler<GetBoardQuery, BoardDto> {
  constructor(
    private readonly boardRepository: BoardRepository,
    private readonly boardDtoService: BoardDtoService,
  ) {}

  async execute(query: GetBoardQuery): Promise<BoardDto> {
    // 获取 board 聚合
    const board = await this.boardRepository.getById(query.boardId);

    // TODO: 权限验证逻辑已暂时移除，等 user 和 space 模块迁移完成后重新添加

    return this.boardDtoService.toBoardDto(board);
  }
}
