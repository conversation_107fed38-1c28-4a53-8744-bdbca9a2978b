import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { BoardWithSomeBoardItemsDto } from '../../../dto/board.dto';
import { BoardRepository } from '../../../repositories/board.repository';
import { BoardDtoService } from '../../dto-services/board-dto.service';
import { ListBoardsWithSomeBoardItemsQuery } from '../../queries/board/list-boards-with-some-board-items.query';

@QueryHandler(ListBoardsWithSomeBoardItemsQuery)
export class ListBoardsWithSomeBoardItemsHandler
  implements IQueryHandler<ListBoardsWithSomeBoardItemsQuery, BoardWithSomeBoardItemsDto[]>
{
  constructor(
    private readonly boardRepository: BoardRepository,
    private readonly boardDtoService: BoardDtoService,
  ) {}

  async execute(query: ListBoardsWithSomeBoardItemsQuery): Promise<BoardWithSomeBoardItemsDto[]> {
    // Get all boards with filters, no limit on board count
    const boards = await this.boardRepository.findBySpaceId(query.spaceId, {
      status: query.status,
      fuzzyName: query.fuzzyName,
    });

    // Convert boards to DTOs with limited board items (10 items per board)
    return this.boardDtoService.toBoardWithSomeBoardItemsDtoList(boards, 10);
  }
}
