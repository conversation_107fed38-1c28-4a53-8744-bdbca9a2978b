import { BadRequestException, Body, Controller, HttpCode, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { BaseController } from '@/shared/base.controller';
import {
  ListSimpleBoardsByEntityIdsDto,
  SimpleBoardWithEntityIdsDto,
} from '../dto/board/list-simple-boards-by-entity-ids.dto';
import {
  BoardDto,
  BoardIdDto,
  BoardWithCountDto,
  BoardWithItemsDto,
  BoardWithSomeBoardItemsDto,
  CreateBoardDto,
  DeleteBoardDto,
  GetBoardDto,
  ListBoardsDto,
  ListBoardsWithSomeBoardItemsDto,
  UpdateBoardDto,
} from '../dto/board.dto';
import { MoveEntityToUnsortedDto } from '../dto/move/move-entity-to-unsorted.dto';
import { BoardRepository } from '../repositories/board.repository';
import { ArchiveBoardCommand } from '../services/commands/board/archive-board.command';
import { CreateBoardCommand } from '../services/commands/board/create-board.command';
import { DeleteBoardCommand } from '../services/commands/board/delete-board.command';
import { PinBoardCommand } from '../services/commands/board/pin-board.command';
import { UnarchiveBoardCommand } from '../services/commands/board/unarchive-board.command';
import { UnpinBoardCommand } from '../services/commands/board/unpin-board.command';
import { UpdateBoardCommand } from '../services/commands/board/update-board.command';
import { MoveEntityToUnsortedCommand } from '../services/commands/move/move-entity-to-unsorted.command';
import { GetBoardQuery } from '../services/queries/board/get-board.query';
import { GetBoardDetailQuery } from '../services/queries/board/get-board-detail.query';
import { ListBoardsQuery } from '../services/queries/board/list-boards.query';
import { ListBoardsWithSomeBoardItemsQuery } from '../services/queries/board/list-boards-with-some-board-items.query';
import { ListSimpleBoardsByEntityIdsQuery } from '../services/queries/board/list-simple-boards-by-entity-ids.query';

@ApiTags('Board')
@Controller('api/v1')
export class BoardController extends BaseController {
  constructor(private readonly boardRepository: BoardRepository) {
    super();
  }

  @Post('createBoard')
  @HttpCode(200)
  @ApiOperation({
    summary: '创建看板',
    description: '创建一个新的看板',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: BoardDto,
  })
  async createBoard(@Body() dto: CreateBoardDto): Promise<BoardDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new CreateBoardCommand(userId, spaceId, dto.name, dto.description, dto.icon);

    return this.commandBus.execute(command);
  }

  @Post('deleteBoard')
  @HttpCode(200)
  @ApiOperation({
    summary: '删除看板',
    description: '删除指定的看板',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async deleteBoard(@Body() dto: DeleteBoardDto): Promise<void> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new DeleteBoardCommand(userId, spaceId, dto.id);
    return this.commandBus.execute(command);
  }

  @Post(['listBoards', 'board/listBoards'])
  @HttpCode(200)
  @ApiOperation({
    summary: '获取看板列表',
    description: '获取用户的看板列表，支持状态过滤和名称搜索',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: [BoardWithCountDto],
  })
  async listBoards(@Body() dto: ListBoardsDto): Promise<BoardWithCountDto[]> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new ListBoardsQuery(userId, spaceId, dto.status, dto.fuzzyName);

    return this.queryBus.execute(query);
  }

  @Post('board/getBoard')
  @HttpCode(200)
  @ApiOperation({
    summary: '获取看板详情',
    description: '获取指定看板的详细信息',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: BoardDto,
  })
  async getBoard(@Body() dto: GetBoardDto): Promise<BoardDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new GetBoardQuery(userId, spaceId, dto.id);
    return this.queryBus.execute(query);
  }

  @Post('board/getBoardDetail')
  @HttpCode(200)
  @ApiOperation({
    summary: '获取看板详细信息',
    description: '获取看板详细信息，包括内容统计和看板项目',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: BoardWithItemsDto,
  })
  async getBoardDetail(@Body() dto: GetBoardDto): Promise<BoardWithItemsDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const query = new GetBoardDetailQuery(userId, spaceId, dto.id);
    return this.queryBus.execute(query);
  }

  @Post('board/patchBoard')
  @HttpCode(200)
  @ApiOperation({
    summary: '更新看板',
    description: '更新看板的基本信息',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: BoardDto,
  })
  async patchBoard(@Body() dto: UpdateBoardDto): Promise<BoardDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new UpdateBoardCommand(
      userId,
      spaceId,
      dto.id,
      dto.name,
      dto.description,
      dto.icon,
      dto.status,
    );

    return this.commandBus.execute(command);
  }

  @Post('board/pinBoard')
  @HttpCode(200)
  @ApiOperation({
    summary: '置顶看板',
    description: '将看板置顶',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: BoardDto,
  })
  async pinBoard(@Body() dto: BoardIdDto): Promise<BoardDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new PinBoardCommand(userId, spaceId, dto.id);
    return this.commandBus.execute(command);
  }

  @Post('board/unpinBoard')
  @HttpCode(200)
  @ApiOperation({
    summary: '取消置顶看板',
    description: '取消看板置顶状态',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: BoardDto,
  })
  async unpinBoard(@Body() dto: BoardIdDto): Promise<BoardDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new UnpinBoardCommand(userId, spaceId, dto.id);
    return this.commandBus.execute(command);
  }

  @Post('board/archiveBoard')
  @HttpCode(200)
  @ApiOperation({
    summary: '归档看板',
    description: '将看板状态设为归档',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: BoardDto,
  })
  async archiveBoard(@Body() dto: BoardIdDto): Promise<BoardDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new ArchiveBoardCommand(userId, spaceId, dto.id);
    return this.commandBus.execute(command);
  }

  @Post('board/unarchiveBoard')
  @HttpCode(200)
  @ApiOperation({
    summary: '取消归档看板',
    description: '将看板状态从归档恢复为进行中',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: BoardDto,
  })
  async unarchiveBoard(@Body() dto: BoardIdDto): Promise<BoardDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new UnarchiveBoardCommand(userId, spaceId, dto.id);
    return this.commandBus.execute(command);
  }

  @Post('board/listBoardsWithSomeBoardItems')
  @HttpCode(200)
  @ApiOperation({
    summary: '获取带有部分内容的看板列表',
    description: '获取看板列表，包含部分看板项目',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: [BoardWithSomeBoardItemsDto],
  })
  async listBoardsWithSomeBoardItems(
    @Body() dto: ListBoardsWithSomeBoardItemsDto,
  ): Promise<BoardWithSomeBoardItemsDto[]> {
    const spaceId = await this.getSpaceId();

    const query = new ListBoardsWithSomeBoardItemsQuery(spaceId, dto.fuzzyName, dto.status);

    return this.queryBus.execute(query);
  }

  @Post('board/getDefaultBoard')
  @HttpCode(200)
  @ApiOperation({
    summary: '获取默认看板',
    description: '获取用户的默认看板',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: BoardDto,
  })
  async getDefaultBoard(): Promise<BoardDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    // 查找默认看板
    const query = new ListBoardsQuery(userId, spaceId);
    const boards = await this.queryBus.execute(query);

    // 查找类型为 default 的看板
    const defaultBoard = boards.find((board) => board.type === 'default');
    return defaultBoard;
  }

  @Post('moveEntityToBoard')
  @HttpCode(200)
  @ApiOperation({
    summary: '移动实体到看板',
    description: '移动 snip 或 thought 到指定看板',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async moveEntityToBoard(
    @Body() dto: { entity_type: string; entity_id: string; target_board_id: string },
  ): Promise<any> {
    // 验证 entity_type
    if (!['snip', 'thought'].includes(dto.entity_type)) {
      throw new BadRequestException('Invalid entity_type. Must be "snip" or "thought"');
    }

    // TODO: 实现实际的移动逻辑
    // 暂时返回成功响应
    return { success: true };
  }

  @Post('moveEntityToUnsorted')
  @HttpCode(200)
  @ApiOperation({
    summary: '移动实体到未分类',
    description: '移动 snip、thought 或其他实体到未分类（默认看板）',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
  })
  async moveEntityToUnsorted(@Body() dto: MoveEntityToUnsortedDto): Promise<void> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    const command = new MoveEntityToUnsortedCommand(userId, spaceId, dto.entityType, dto.entityId);

    return this.commandBus.execute(command);
  }

  @Post('createBoardFromTemplate')
  @HttpCode(200)
  @ApiOperation({
    summary: '从模板创建看板',
    description: '根据模板创建新看板',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: BoardDto,
  })
  async createBoardFromTemplate(@Body() dto: { template: string }): Promise<BoardDto> {
    const userId = this.getUserId();
    const spaceId = await this.getSpaceId();

    // 简单实现：根据模板名称创建一个预设的看板
    const templateName = dto.template === 'template-1' ? 'Template Board 1' : 'Template Board';

    const command = new CreateBoardCommand(
      userId,
      spaceId,
      templateName,
      'Board created from template',
      { name: 'template', color: '#8B5CF6' },
    );

    return this.commandBus.execute(command);
  }

  @Post('listSimpleBoardsByEntityIds')
  @HttpCode(200)
  @ApiOperation({
    summary: '根据实体ID列表获取简化板块信息',
    description:
      '根据 snip 和 thought ID 列表获取相关板块的简化信息（仅包含ID、名称、图标等部分字段）',
  })
  @ApiResponse({
    status: 200,
    description: 'Success',
    type: [SimpleBoardWithEntityIdsDto],
  })
  async listSimpleBoardsByEntityIds(
    @Body() dto: ListSimpleBoardsByEntityIdsDto,
  ): Promise<SimpleBoardWithEntityIdsDto[]> {
    const query = new ListSimpleBoardsByEntityIdsQuery(dto.snipIds || [], dto.thoughtIds || []);

    return this.queryBus.execute(query);
  }
}
