import { Injectable, NotFoundException } from '@nestjs/common';
import { and, desc, eq, inArray, isNull } from 'drizzle-orm';
import { BoardStatusEnum } from '@/common/types';
import { DatabaseService } from '@/shared/db/database.service';
import { boardItems, boards, thoughts, Visibility } from '@/shared/db/public.schema';
import { BoardPositionInfo } from '../domain/shared/board-position.service';
import { Thought, ThoughtContent, TitleType } from '../domain/thought/models/thought.entity';

export interface ThoughtFilter {
  spaceId: string;
  creatorId?: string;
  visibility?: Visibility;
  relatedSnipId?: string;
}

// Database types
type ThoughtDO = typeof thoughts.$inferSelect;
type BoardItemDO = typeof boardItems.$inferSelect;

@Injectable()
export class ThoughtRepository {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  /**
   * 保存想法聚合 - 新版本，支持位置管理
   */
  async save(thought: Thought, trx?: any): Promise<void> {
    const thoughtDO = this.entityToDO(thought);
    const db = trx || this.db;

    if (thought.isNew) {
      // 插入新记录
      await db.insert(thoughts).values(thoughtDO);

      // 创建 board_item 关联
      const boardItemId = await this.saveBoardItem('thought', thought.id, thought.position, trx);
      // 更新位置信息中的 boardItemId
      thought.position.boardItemId = boardItemId;

      thought.markAsExisting();
    } else {
      // 更新现有记录
      const { id, ...shouldUpdate } = thoughtDO;
      await db
        .update(thoughts)
        .set(shouldUpdate)
        .where(and(isNull(thoughts.deletedAt), eq(thoughts.id, id)));

      // 同步 board_item 信息
      if (thought.position) {
        await this.upsertBoardItem('thought', thought.id, thought.position, trx);
      } else {
        // 如果位置被移除，删除 board_item
        await db.delete(boardItems).where(eq(boardItems.thoughtId, thought.id));
      }
    }
  }

  /**
   * 根据 ID 查找想法
   */
  async findById(id: string): Promise<Thought | undefined> {
    const result = await this.db
      .select({
        // thought fields
        id: thoughts.id,
        createdAt: thoughts.createdAt,
        updatedAt: thoughts.updatedAt,
        deletedAt: thoughts.deletedAt,
        spaceId: thoughts.spaceId,
        creatorId: thoughts.creatorId,
        title: thoughts.title,
        titleType: thoughts.titleType,
        contentRaw: thoughts.contentRaw,
        contentPlain: thoughts.contentPlain,
        visibility: thoughts.visibility,
        // board_item fields
        boardItemId: boardItems.id,
        boardItemBoardId: boardItems.boardId,
        boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
        boardItemBoardGroupId: boardItems.boardGroupId,
        boardItemRank: boardItems.rank,
        boardItemCreatedAt: boardItems.createdAt,
        boardItemUpdatedAt: boardItems.updatedAt,
      })
      .from(thoughts)
      .innerJoin(
        boardItems,
        and(eq(boardItems.thoughtId, thoughts.id), isNull(boardItems.deletedAt)),
      )
      .where(and(isNull(thoughts.deletedAt), eq(thoughts.id, id)));

    if (result.length === 0) {
      return undefined;
    }

    return this.doToEntity(result[0]);
  }

  /**
   * 根据 ID 获取想法（不存在则抛异常）
   */
  async getById(id: string): Promise<Thought> {
    const thought = await this.findById(id);
    if (!thought) {
      throw new NotFoundException(`Thought with id ${id} not found`);
    }
    return thought;
  }

  /**
   * 根据条件查找想法列表
   */
  async findByFilter(filter: ThoughtFilter): Promise<Thought[]> {
    const conditions = [isNull(thoughts.deletedAt), eq(thoughts.spaceId, filter.spaceId)];

    if (filter.creatorId) {
      conditions.push(eq(thoughts.creatorId, filter.creatorId));
    }

    if (filter.visibility) {
      conditions.push(eq(thoughts.visibility, filter.visibility));
    }

    const result = await this.db
      .select({
        // thought fields
        id: thoughts.id,
        createdAt: thoughts.createdAt,
        updatedAt: thoughts.updatedAt,
        deletedAt: thoughts.deletedAt,
        spaceId: thoughts.spaceId,
        creatorId: thoughts.creatorId,
        title: thoughts.title,
        titleType: thoughts.titleType,
        contentRaw: thoughts.contentRaw,
        contentPlain: thoughts.contentPlain,
        visibility: thoughts.visibility,
        // board_item fields
        boardItemId: boardItems.id,
        boardItemBoardId: boardItems.boardId,
        boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
        boardItemBoardGroupId: boardItems.boardGroupId,
        boardItemRank: boardItems.rank,
        boardItemCreatedAt: boardItems.createdAt,
        boardItemUpdatedAt: boardItems.updatedAt,
      })
      .from(thoughts)
      .innerJoin(
        boardItems,
        and(eq(boardItems.thoughtId, thoughts.id), isNull(boardItems.deletedAt)),
      )
      .where(and(...conditions));

    return result.map((row) => this.doToEntity(row));
  }

  /**
   * 获取空间内所有想法
   */
  async findBySpaceId(spaceId: string): Promise<Thought[]> {
    const result = await this.db
      .select({
        // thought fields
        id: thoughts.id,
        createdAt: thoughts.createdAt,
        updatedAt: thoughts.updatedAt,
        deletedAt: thoughts.deletedAt,
        spaceId: thoughts.spaceId,
        creatorId: thoughts.creatorId,
        title: thoughts.title,
        titleType: thoughts.titleType,
        contentRaw: thoughts.contentRaw,
        contentPlain: thoughts.contentPlain,
        visibility: thoughts.visibility,
        // board_item fields
        boardItemId: boardItems.id,
        boardItemBoardId: boardItems.boardId,
        boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
        boardItemBoardGroupId: boardItems.boardGroupId,
        boardItemRank: boardItems.rank,
        boardItemCreatedAt: boardItems.createdAt,
        boardItemUpdatedAt: boardItems.updatedAt,
      })
      .from(thoughts)
      .innerJoin(
        boardItems,
        and(eq(boardItems.thoughtId, thoughts.id), isNull(boardItems.deletedAt)),
      )
      .where(and(isNull(thoughts.deletedAt), eq(thoughts.spaceId, spaceId)));

    return result.map((row) => this.doToEntity(row));
  }

  /**
   * 根据空间 ID 查找未使用的 Thought (未添加到任何 board 的 Thought)
   * 参考 youapp 的 selectUnused 实现
   */
  async findUnusedBySpaceId(spaceId: string, limit?: number): Promise<Thought[]> {
    const baseQuery = this.db
      .select({
        // thought fields
        id: thoughts.id,
        createdAt: thoughts.createdAt,
        updatedAt: thoughts.updatedAt,
        deletedAt: thoughts.deletedAt,
        spaceId: thoughts.spaceId,
        creatorId: thoughts.creatorId,
        title: thoughts.title,
        titleType: thoughts.titleType,
        contentRaw: thoughts.contentRaw,
        contentPlain: thoughts.contentPlain,
        visibility: thoughts.visibility,
        // board_item fields - 由于是 LEFT JOIN，这些字段可能为 null
        boardItemId: boardItems.id,
        boardItemBoardId: boardItems.boardId,
        boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
        boardItemBoardGroupId: boardItems.boardGroupId,
        boardItemRank: boardItems.rank,
        boardItemCreatedAt: boardItems.createdAt,
        boardItemUpdatedAt: boardItems.updatedAt,
      })
      .from(thoughts)
      .leftJoin(
        boardItems,
        and(eq(boardItems.thoughtId, thoughts.id), isNull(boardItems.deletedAt)),
      )
      .where(
        and(
          isNull(thoughts.deletedAt),
          eq(thoughts.spaceId, spaceId),
          isNull(boardItems.id), // 未关联到任何 board_item 的 thought 被认为是 unused
        ),
      )
      .orderBy(desc(thoughts.updatedAt))
      .$dynamic();

    // 如果提供了 limit 参数才进行限制，否则返回所有结果（按照原 youapp 逻辑）
    const result = limit !== undefined ? await baseQuery.limit(limit) : await baseQuery;

    return result.map((row) => this.doToEntity(row));
  }

  /**
   * 根据空间 ID 查找进行中的 Thought (在进行中状态的板子里的 Thought)
   * 参考 youapp 的 selectInProgress 实现
   */
  async findInProgressBySpaceId(spaceId: string): Promise<Thought[]> {
    const result = await this.db
      .select({
        // thought fields
        id: thoughts.id,
        createdAt: thoughts.createdAt,
        updatedAt: thoughts.updatedAt,
        deletedAt: thoughts.deletedAt,
        spaceId: thoughts.spaceId,
        creatorId: thoughts.creatorId,
        title: thoughts.title,
        titleType: thoughts.titleType,
        contentRaw: thoughts.contentRaw,
        contentPlain: thoughts.contentPlain,
        visibility: thoughts.visibility,
        // board_item fields
        boardItemId: boardItems.id,
        boardItemBoardId: boardItems.boardId,
        boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
        boardItemBoardGroupId: boardItems.boardGroupId,
        boardItemRank: boardItems.rank,
        boardItemCreatedAt: boardItems.createdAt,
        boardItemUpdatedAt: boardItems.updatedAt,
      })
      .from(thoughts)
      .innerJoin(
        boardItems,
        and(eq(boardItems.thoughtId, thoughts.id), isNull(boardItems.deletedAt)),
      )
      .innerJoin(boards, and(eq(boards.id, boardItems.boardId), isNull(boards.deletedAt)))
      .where(
        and(
          isNull(thoughts.deletedAt),
          eq(thoughts.spaceId, spaceId),
          eq(boards.status, BoardStatusEnum.IN_PROGRESS),
        ),
      )
      .orderBy(desc(thoughts.createdAt));

    return result.map((row) => this.doToEntity(row));
  }

  /**
   * 根据 boardItemId 查找想法
   */
  async findByBoardItemId(boardItemId: string): Promise<Thought | undefined> {
    const result = await this.db
      .select({
        // thought fields
        id: thoughts.id,
        createdAt: thoughts.createdAt,
        updatedAt: thoughts.updatedAt,
        deletedAt: thoughts.deletedAt,
        spaceId: thoughts.spaceId,
        creatorId: thoughts.creatorId,
        title: thoughts.title,
        titleType: thoughts.titleType,
        contentRaw: thoughts.contentRaw,
        contentPlain: thoughts.contentPlain,
        visibility: thoughts.visibility,
        // board_item fields
        boardItemId: boardItems.id,
        boardItemBoardId: boardItems.boardId,
        boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
        boardItemBoardGroupId: boardItems.boardGroupId,
        boardItemRank: boardItems.rank,
        boardItemCreatedAt: boardItems.createdAt,
        boardItemUpdatedAt: boardItems.updatedAt,
      })
      .from(thoughts)
      .innerJoin(
        boardItems,
        and(eq(boardItems.thoughtId, thoughts.id), isNull(boardItems.deletedAt)),
      )
      .where(and(isNull(thoughts.deletedAt), eq(boardItems.id, boardItemId)));

    if (result.length === 0) {
      return undefined;
    }

    return this.doToEntity(result[0]);
  }

  /**
   * 批量查询想法 - 用于优化性能（返回完整内容）
   */
  async findByIds(ids: string[]): Promise<Thought[]> {
    if (ids.length === 0) {
      return [];
    }

    const result = await this.db
      .select({
        // thought fields
        id: thoughts.id,
        createdAt: thoughts.createdAt,
        updatedAt: thoughts.updatedAt,
        deletedAt: thoughts.deletedAt,
        spaceId: thoughts.spaceId,
        creatorId: thoughts.creatorId,
        title: thoughts.title,
        titleType: thoughts.titleType,
        contentRaw: thoughts.contentRaw,
        contentPlain: thoughts.contentPlain,
        visibility: thoughts.visibility,
        // board_item fields
        boardItemId: boardItems.id,
        boardItemBoardId: boardItems.boardId,
        boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
        boardItemBoardGroupId: boardItems.boardGroupId,
        boardItemRank: boardItems.rank,
        boardItemCreatedAt: boardItems.createdAt,
        boardItemUpdatedAt: boardItems.updatedAt,
      })
      .from(thoughts)
      .leftJoin(
        boardItems,
        and(eq(boardItems.thoughtId, thoughts.id), isNull(boardItems.deletedAt)),
      )
      .where(and(isNull(thoughts.deletedAt), inArray(thoughts.id, ids)));

    return result.map((row) => this.doToEntity(row));
  }

  /**
   * 删除想法（软删除）
   */
  async deleteById(id: string): Promise<void> {
    const now = new Date();

    // 1. 软删除想法记录
    await this.db
      .update(thoughts)
      .set({
        deletedAt: now,
      } as any)
      .where(and(isNull(thoughts.deletedAt), eq(thoughts.id, id)));

    // 2. 软删除关联的 board_items 记录
    await this.db
      .update(boardItems)
      .set({
        deletedAt: now,
        updatedAt: now,
      } as any)
      .where(and(isNull(boardItems.deletedAt), eq(boardItems.thoughtId, id)));
  }

  /**
   * 批量删除想法（软删除）
   */
  async deleteManyByIds(ids: string[]): Promise<void> {
    if (ids.length === 0) {
      return;
    }

    const now = new Date();

    // 1. 软删除想法记录
    await this.db
      .update(thoughts)
      .set({
        deletedAt: now,
      } as any)
      .where(and(isNull(thoughts.deletedAt), inArray(thoughts.id, ids)));

    // 2. 软删除关联的 board_items 记录
    await this.db
      .update(boardItems)
      .set({
        deletedAt: now,
        updatedAt: now,
      } as any)
      .where(and(isNull(boardItems.deletedAt), inArray(boardItems.thoughtId, ids)));
  }

  /**
   * 实体转换为 DO
   */
  private entityToDO(thought: Thought): ThoughtDO {
    return {
      id: thought.id,
      createdAt: thought.createdAt,
      updatedAt: thought.updatedAt,
      deletedAt: null, // 软删除字段由仓储层控制
      spaceId: thought.spaceId,
      creatorId: thought.creatorId,
      title: thought.title,
      titleType: thought.titleType,
      contentRaw: thought.content?.raw || '',
      contentPlain: thought.content?.plain || null,
      visibility: thought.visibility,
    };
  }

  /**
   * DO 转换为实体 - 支持新的位置信息
   */
  private doToEntity(row: any): Thought {
    const content: ThoughtContent = {
      raw: row.contentRaw || '',
      plain: row.contentPlain || undefined,
    };

    // 构建位置信息
    const position: BoardPositionInfo | undefined = row.boardItemId
      ? {
          boardId: row.boardItemBoardId,
          rank: row.boardItemRank,
          parentBoardGroupId: row.boardItemParentBoardGroupId,
          boardItemId: row.boardItemId,
        }
      : undefined;

    // 创建实体
    const thought = new Thought(
      row.id,
      row.spaceId,
      row.creatorId,
      row.createdAt,
      row.updatedAt,
      row.title || '',
      row.titleType as TitleType,
      content,
      row.visibility as Visibility,
      position, // 新的位置信息
    );

    // 从数据库加载的实体标记为已存在
    thought.markAsExisting();
    return thought;
  }

  /**
   * 保存 board_item 记录 - 统一的位置管理
   */
  private async saveBoardItem(
    entityType: 'thought' | 'board_group' | 'snip',
    entityId: string,
    position: BoardPositionInfo,
    trx?: any,
  ): Promise<string> {
    const db = trx || this.db;

    const boardItemDO = {
      // 不设置 id，让数据库自动生成
      boardId: position.boardId,
      rank: position.rank,
      parentBoardGroupId: position.parentBoardGroupId || null, // 明确转换 undefined 为 null
      thoughtId: entityType === 'thought' ? entityId : null,
      snipId: entityType === 'snip' ? entityId : null,
      boardGroupId: entityType === 'board_group' ? entityId : null,
      chatId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

    const result = await db.insert(boardItems).values(boardItemDO).returning({ id: boardItems.id });
    return result[0].id;
  }

  /**
   * 更新或插入 board_item 记录
   */
  private async upsertBoardItem(
    entityType: 'thought' | 'board_group' | 'snip',
    entityId: string,
    position: BoardPositionInfo,
    trx?: any,
  ): Promise<string | undefined> {
    const db = trx || this.db;

    const entityIdField =
      entityType === 'thought'
        ? 'thoughtId'
        : entityType === 'snip'
          ? 'snipId'
          : entityType === 'board_group'
            ? 'boardGroupId'
            : 'chatId';

    // 检查是否已存在
    const existing = await db
      .select()
      .from(boardItems)
      .where(eq(boardItems[entityIdField], entityId))
      .limit(1);

    const boardItemData = {
      boardId: position.boardId,
      rank: position.rank,
      parentBoardGroupId: position.parentBoardGroupId || null, // 明确转换 undefined 为 null
      updatedAt: new Date(),
    };

    if (existing.length > 0) {
      // 更新现有记录
      await db.update(boardItems).set(boardItemData).where(eq(boardItems[entityIdField], entityId));
      return existing[0].id; // 返回现有的 boardItemId
    } else {
      // 插入新记录
      const boardItemId = await this.saveBoardItem(entityType, entityId, position, trx);
      return boardItemId;
    }
  }
}
