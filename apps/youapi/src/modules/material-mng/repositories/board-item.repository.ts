import { Injectable, NotFoundException } from '@nestjs/common';
import { and, asc, count, eq, gt, isNull } from 'drizzle-orm';
import { DatabaseService } from '@/shared/db/database.service';
import { boardItems } from '@/shared/db/public.schema';
import { BoardItem, BoardItemType } from '../domain/board-item/models/board-item.entity';

// Database types
type BoardItemDO = typeof boardItems.$inferSelect;

@Injectable()
export class BoardItemRepository {
  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  /**
   * 保存 BoardItem 聚合
   */
  async save(boardItem: BoardItem): Promise<void> {
    const boardItemDO = this.entityToDO(boardItem);

    if (boardItem.isNew) {
      await this.db.insert(boardItems).values(boardItemDO);
      boardItem.markAsExisting();
    } else {
      await this.db.update(boardItems).set(boardItemDO).where(eq(boardItems.id, boardItem.id));
    }
  }

  /**
   * 根据 ID 查找 BoardItem
   */
  async findById(id: string): Promise<BoardItem | undefined> {
    const result = await this.db
      .select()
      .from(boardItems)
      .where(and(eq(boardItems.id, id), isNull(boardItems.deletedAt)));

    if (result.length === 0) {
      return undefined;
    }

    return this.doToEntity(result[0]);
  }

  /**
   * 根据 ID 获取 BoardItem，不存在时抛出异常
   */
  async getById(id: string): Promise<BoardItem> {
    const result = await this.findById(id);
    if (!result) {
      throw new NotFoundException(`BoardItem with id ${id} not found`);
    }
    return result;
  }

  /**
   * 根据实体类型和实体 ID 查找 BoardItem
   */
  async findByEntityId(
    entityType: BoardItemType,
    entityId: string,
  ): Promise<BoardItem | undefined> {
    const whereConditions = [isNull(boardItems.deletedAt)];

    switch (entityType) {
      case BoardItemType.SNIP:
        whereConditions.push(eq(boardItems.snipId, entityId));
        break;
      case BoardItemType.THOUGHT:
        whereConditions.push(eq(boardItems.thoughtId, entityId));
        break;
      case BoardItemType.BOARD_GROUP:
        whereConditions.push(eq(boardItems.boardGroupId, entityId));
        break;
      case BoardItemType.CHAT:
        whereConditions.push(eq(boardItems.chatId, entityId));
        break;
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }

    const result = await this.db
      .select()
      .from(boardItems)
      .where(and(...whereConditions));

    if (result.length === 0) {
      return undefined;
    }

    return this.doToEntity(result[0]);
  }

  /**
   * 根据父 BoardGroup ID 列出 BoardItem
   */
  async listByParentBoardGroupId(parentBoardGroupId: string): Promise<BoardItem[]> {
    const result = await this.db
      .select()
      .from(boardItems)
      .where(
        and(eq(boardItems.parentBoardGroupId, parentBoardGroupId), isNull(boardItems.deletedAt)),
      )
      .orderBy(asc(boardItems.rank));

    return result.map(this.doToEntity);
  }

  /**
   * 根据 Board ID 列出所有 BoardItem
   */
  async listByBoardId(boardId: string): Promise<BoardItem[]> {
    const result = await this.db
      .select()
      .from(boardItems)
      .where(and(eq(boardItems.boardId, boardId), isNull(boardItems.deletedAt)))
      .orderBy(asc(boardItems.rank));

    return result.map(this.doToEntity);
  }

  /**
   * 获取排序范围，用于计算新的排序值
   */
  async getRankRange(
    parentBoardGroupId: string | null,
    rankAfterId?: string,
  ): Promise<[string | undefined, string | undefined]> {
    if (!rankAfterId) {
      // 如果没有指定 rankAfterId，插入到开头
      const firstItem = await this.db
        .select()
        .from(boardItems)
        .where(
          and(
            parentBoardGroupId
              ? eq(boardItems.parentBoardGroupId, parentBoardGroupId)
              : isNull(boardItems.parentBoardGroupId),
            isNull(boardItems.deletedAt),
          ),
        )
        .orderBy(asc(boardItems.rank))
        .limit(1);

      return [undefined, firstItem[0]?.rank];
    }

    // 找到 rankAfterId 的位置，然后找到下一个项目
    const rankAfterItem = await this.findById(rankAfterId);
    if (!rankAfterItem) {
      throw new NotFoundException(`BoardItem with id ${rankAfterId} not found`);
    }

    const nextItem = await this.db
      .select()
      .from(boardItems)
      .where(
        and(
          parentBoardGroupId
            ? eq(boardItems.parentBoardGroupId, parentBoardGroupId)
            : isNull(boardItems.parentBoardGroupId),
          isNull(boardItems.deletedAt),
          gt(boardItems.rank, rankAfterItem.rank),
        ),
      )
      .orderBy(asc(boardItems.rank))
      .limit(1);

    return [rankAfterItem.rank, nextItem[0]?.rank];
  }

  /**
   * 游标分页查询 - 排除 BoardGroup
   * 对应 youapp 的 selectByCursorExcludeGroup 方法
   */
  async findByCursorExcludeGroup(
    boardId: string,
    limit: number = 10,
    startingAfter?: string,
  ): Promise<{ data: BoardItem[]; hasMore: boolean }> {
    const conditions = [
      eq(boardItems.boardId, boardId),
      isNull(boardItems.deletedAt),
      isNull(boardItems.boardGroupId), // 排除 board group
    ];

    if (startingAfter) {
      conditions.push(gt(boardItems.id, startingAfter));
    }

    const results = await this.db
      .select()
      .from(boardItems)
      .where(and(...conditions))
      .orderBy(asc(boardItems.id))
      .limit(limit + 1); // 查询多一条以判断是否有更多

    const hasMore = results.length > limit;
    const data = results.slice(0, limit).map(this.doToEntity);

    return { data, hasMore };
  }

  /**
   * 统计非 BoardGroup 的 BoardItem 数量
   * 对应 youapp 的 countExcludeGroup 方法
   */
  async countExcludeGroup(boardId: string): Promise<number> {
    const result = await this.db
      .select({ count: count() })
      .from(boardItems)
      .where(
        and(
          eq(boardItems.boardId, boardId),
          isNull(boardItems.deletedAt),
          isNull(boardItems.boardGroupId), // 排除 board group
        ),
      );

    return result[0]?.count || 0;
  }

  /**
   * 软删除 BoardItem
   */
  async delete(id: string): Promise<void> {
    await this.db
      .update(boardItems)
      .set({ deletedAt: new Date() } as any)
      .where(eq(boardItems.id, id));
  }

  /**
   * 根据实体类型和实体 ID 软删除 BoardItem
   */
  async deleteByEntityId(entityType: BoardItemType, entityId: string): Promise<void> {
    const whereConditions = [];

    switch (entityType) {
      case BoardItemType.SNIP:
        whereConditions.push(eq(boardItems.snipId, entityId));
        break;
      case BoardItemType.THOUGHT:
        whereConditions.push(eq(boardItems.thoughtId, entityId));
        break;
      case BoardItemType.BOARD_GROUP:
        whereConditions.push(eq(boardItems.boardGroupId, entityId));
        break;
      case BoardItemType.CHAT:
        whereConditions.push(eq(boardItems.chatId, entityId));
        break;
      default:
        throw new Error(`Unsupported entity type: ${entityType}`);
    }

    await this.db
      .update(boardItems)
      .set({ deletedAt: new Date() } as any)
      .where(and(isNull(boardItems.deletedAt), ...whereConditions));
  }

  /**
   * 实体转换为 DO
   */
  private entityToDO(
    boardItem: BoardItem,
  ): Omit<BoardItemDO, 'createdAt' | 'updatedAt' | 'deletedAt'> {
    const result: any = {
      id: boardItem.id,
      boardId: boardItem.boardId,
      parentBoardGroupId: boardItem.parentBoardGroupId,
      rank: boardItem.rank,
      snipId: null,
      thoughtId: null,
      boardGroupId: null,
      chatId: null,
    };

    // 根据实体类型设置对应的 ID
    switch (boardItem.entityType) {
      case BoardItemType.SNIP:
        result.snipId = boardItem.entityId;
        break;
      case BoardItemType.THOUGHT:
        result.thoughtId = boardItem.entityId;
        break;
      case BoardItemType.BOARD_GROUP:
        result.boardGroupId = boardItem.entityId;
        break;
      case BoardItemType.CHAT:
        result.chatId = boardItem.entityId;
        break;
    }

    return result;
  }

  /**
   * DO 转换为实体
   */
  private doToEntity(boardItemDO: BoardItemDO): BoardItem {
    let entityType: BoardItemType;
    let entityId: string;

    if (boardItemDO.snipId) {
      entityType = BoardItemType.SNIP;
      entityId = boardItemDO.snipId;
    } else if (boardItemDO.thoughtId) {
      entityType = BoardItemType.THOUGHT;
      entityId = boardItemDO.thoughtId;
    } else if (boardItemDO.boardGroupId) {
      entityType = BoardItemType.BOARD_GROUP;
      entityId = boardItemDO.boardGroupId;
    } else if (boardItemDO.chatId) {
      entityType = BoardItemType.CHAT;
      entityId = boardItemDO.chatId;
    } else {
      throw new Error(`Invalid BoardItem data: no entity ID found for ${boardItemDO.id}`);
    }

    const boardItem = new BoardItem(
      boardItemDO.id,
      boardItemDO.createdAt,
      boardItemDO.updatedAt,
      boardItemDO.boardId,
      boardItemDO.parentBoardGroupId,
      entityType,
      entityId,
      boardItemDO.rank,
    );

    boardItem.markAsExisting();
    return boardItem;
  }
}
