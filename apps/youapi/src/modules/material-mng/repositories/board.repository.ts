import { Injectable, NotFoundException } from '@nestjs/common';
import { and, asc, count, desc, eq, ilike, inArray, isNotNull, isNull, or, sql } from 'drizzle-orm';

import { DatabaseService } from '@/shared/db/database.service';
import { boardItems, boards } from '@/shared/db/public.schema';
import { Board, BoardIcon, BoardStatus, BoardType } from '../domain/board/models/board.entity';
import { SimpleBoard } from '../domain/board/types/simple-board.types';

// Board Data Object
interface BoardDO {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
  spaceId: string;
  creatorId: string;
  name: string;
  description: string;
  iconName: string;
  iconColor: string;
  pinnedAt: Date | null;
  status: BoardStatus;
  heroImageUrls: string[] | null;
  intro: string | null;
  rank: string;
  type: BoardType;
}

@Injectable()
export class BoardRepository {
  private get db() {
    return this.databaseService.db;
  }

  constructor(private readonly databaseService: DatabaseService) {}

  async save(board: Board): Promise<void> {
    const boardDO = this.entityToDO(board);

    if (board.isNew) {
      await this.db.insert(boards).values(boardDO);
      board.markAsExisting();
    } else {
      await this.db.update(boards).set(boardDO).where(eq(boards.id, board.id));
    }
  }

  async findById(id: string): Promise<Board | undefined> {
    const result = await this.db
      .select()
      .from(boards)
      .where(and(eq(boards.id, id), isNull(boards.deletedAt)))
      .limit(1);

    if (result.length === 0) {
      return undefined;
    }

    return this.doToEntity(result[0]);
  }

  async getById(id: string): Promise<Board> {
    const board = await this.findById(id);
    if (!board) {
      throw new NotFoundException(`Board with id ${id} not found`);
    }
    return board;
  }

  async findBySpaceId(
    spaceId: string,
    options?: {
      status?: BoardStatus;
      fuzzyName?: string;
      limit?: number;
      offset?: number;
    },
  ): Promise<Board[]> {
    const conditions = [eq(boards.spaceId, spaceId), isNull(boards.deletedAt)];

    if (options?.status) {
      conditions.push(eq(boards.status, options.status));
    }

    if (options?.fuzzyName) {
      conditions.push(ilike(boards.name, `%${options.fuzzyName}%`));
    }

    // 构建基础查询
    const queryParts = {
      select: this.db
        .select()
        .from(boards)
        .where(and(...conditions))
        .orderBy(
          desc(boards.pinnedAt), // 置顶的优先
          asc(boards.rank), // 然后按 rank 排序
          desc(boards.createdAt), // 最后按创建时间倒序
        ),
    };

    // 如果有分页参数，则应用
    if (options?.limit !== undefined && options?.offset !== undefined) {
      const results = await queryParts.select.limit(options.limit).offset(options.offset);
      return results.map((result) => this.doToEntity(result));
    } else if (options?.limit !== undefined) {
      const results = await queryParts.select.limit(options.limit);
      return results.map((result) => this.doToEntity(result));
    } else if (options?.offset !== undefined) {
      const results = await queryParts.select.offset(options.offset);
      return results.map((result) => this.doToEntity(result));
    } else {
      const results = await queryParts.select;
      return results.map((result) => this.doToEntity(result));
    }
  }

  async findByCreatorId(creatorId: string): Promise<Board[]> {
    const results = await this.db
      .select()
      .from(boards)
      .where(and(eq(boards.creatorId, creatorId), isNull(boards.deletedAt)))
      .orderBy(desc(boards.pinnedAt), asc(boards.rank), desc(boards.createdAt));

    return results.map((result) => this.doToEntity(result));
  }

  async findDefaultBoardBySpaceId(spaceId: string): Promise<Board | undefined> {
    const result = await this.db
      .select()
      .from(boards)
      .where(
        and(
          eq(boards.spaceId, spaceId),
          eq(boards.type, BoardType.Default),
          isNull(boards.deletedAt),
        ),
      )
      .limit(1);

    if (result.length === 0) {
      return undefined;
    }

    return this.doToEntity(result[0]);
  }

  async getDefaultBoardBySpaceId(spaceId: string): Promise<Board> {
    const board = await this.findDefaultBoardBySpaceId(spaceId);
    if (!board) {
      throw new NotFoundException(`Default board for space ${spaceId} not found`);
    }
    return board;
  }

  async countBySpaceId(spaceId: string, status?: BoardStatus): Promise<number> {
    const conditions = [eq(boards.spaceId, spaceId), isNull(boards.deletedAt)];

    if (status) {
      conditions.push(eq(boards.status, status));
    }

    const result = await this.db
      .select({ count: count() })
      .from(boards)
      .where(and(...conditions));

    return result[0]?.count || 0;
  }

  async delete(id: string): Promise<void> {
    await this.db
      .update(boards)
      .set({
        deletedAt: new Date(),
        updatedAt: new Date(),
      } as any)
      .where(eq(boards.id, id));
  }

  async countItemsByBoardIds(
    boardIds: string[],
  ): Promise<Record<string, { snipsCount: number; thoughtsCount: number }>> {
    if (boardIds.length === 0) {
      return {};
    }

    const results = await this.db
      .select({
        boardId: boardItems.boardId,
        snipsCount: count(boardItems.snipId),
        thoughtsCount: count(boardItems.thoughtId),
      })
      .from(boardItems)
      .where(and(isNull(boardItems.deletedAt), inArray(boardItems.boardId, boardIds)))
      .groupBy(boardItems.boardId);

    const countsMap: Record<string, { snipsCount: number; thoughtsCount: number }> = {};

    // 初始化所有 board 的计数为 0
    boardIds.forEach((boardId) => {
      countsMap[boardId] = { snipsCount: 0, thoughtsCount: 0 };
    });

    // 填充实际计数
    results.forEach((result) => {
      countsMap[result.boardId] = {
        snipsCount: result.snipsCount,
        thoughtsCount: result.thoughtsCount,
      };
    });

    return countsMap;
  }

  private entityToDO(board: Board): BoardDO {
    return {
      id: board.id,
      createdAt: board.createdAt,
      updatedAt: board.updatedAt,
      deletedAt: board.deletedAt,
      spaceId: board.spaceId,
      creatorId: board.creatorId,
      name: board.name,
      description: board.description,
      iconName: board.icon.name,
      iconColor: board.icon.color,
      pinnedAt: board.pinnedAt,
      status: board.status,
      heroImageUrls: board.heroImageUrls,
      intro: board.intro,
      rank: board.rank,
      type: board.type,
    };
  }

  private doToEntity(boardDO: any): Board {
    const icon: BoardIcon = {
      name: boardDO.iconName,
      color: boardDO.iconColor,
    };

    const board = new Board(
      boardDO.id,
      boardDO.createdAt,
      boardDO.updatedAt,
      boardDO.spaceId,
      boardDO.creatorId,
      boardDO.name,
      boardDO.description,
      icon,
      boardDO.pinnedAt,
      boardDO.status,
      boardDO.heroImageUrls,
      boardDO.intro,
      boardDO.rank,
      boardDO.type,
      boardDO.deletedAt,
    );

    board.markAsExisting();
    return board;
  }

  /**
   * 根据实体ID列表查询简化的板块信息
   * 对应 youapp 中的 selectBoardsInfoByEntityIds 方法
   */
  async findSimpleBoardsByEntityIds(
    snipIds: string[],
    thoughtIds: string[],
  ): Promise<SimpleBoard[]> {
    // 如果两个数组都为空，直接返回空数组
    if ((!snipIds || snipIds.length === 0) && (!thoughtIds || thoughtIds.length === 0)) {
      return [];
    }

    const results = await this.db
      .select({
        id: boards.id,
        name: boards.name,
        iconName: boards.iconName,
        iconColor: boards.iconColor,
        snipIds: sql<string[]>`array_agg(${boardItems.snipId})`,
        thoughtIds: sql<string[]>`array_agg(${boardItems.thoughtId})`,
      })
      .from(boards)
      .innerJoin(boardItems, eq(boards.id, boardItems.boardId))
      .where(
        and(
          isNull(boards.deletedAt),
          isNull(boardItems.deletedAt),
          or(
            ...[
              snipIds && snipIds.length > 0
                ? and(inArray(boardItems.snipId, snipIds), isNotNull(boardItems.snipId))
                : null,
              thoughtIds && thoughtIds.length > 0
                ? and(inArray(boardItems.thoughtId, thoughtIds), isNotNull(boardItems.thoughtId))
                : null,
            ].filter(Boolean),
          ),
        ),
      )
      .groupBy(boards.id);

    // 转换结果并过滤 null 值
    return results.map((result) => ({
      id: result.id,
      name: result.name,
      iconName: result.iconName,
      iconColor: result.iconColor,
      snipIds: result.snipIds?.filter((id) => id !== null) || [],
      thoughtIds: result.thoughtIds?.filter((id) => id !== null) || [],
    }));
  }
}
