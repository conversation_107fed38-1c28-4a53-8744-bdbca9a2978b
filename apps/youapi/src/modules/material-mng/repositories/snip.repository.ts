/**
 * Snip Repository - Snip 数据访问层
 * 负责 Snip 聚合的持久化操作，包括基础 CRUD 和复杂查询
 *
 * Migrated from:
 * - /Users/<USER>/ProjectCode/CursorProject/youapp/src/lib/dao/snip/index.ts
 */

import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { and, count, desc, eq, gt, inArray, isNull, like, lt, sql } from 'drizzle-orm';
import { BoardStatusEnum } from '@/common/types';
import { SafeParse } from '@/common/utils';
import { boardItems, boards, snips, Visibility } from '@/shared/db/public.schema';
import { DatabaseService } from '../../../shared/db/database.service';
import { BoardPositionInfo } from '../domain/shared/board-position.service';
import { Article } from '../domain/snip/models/article.entity';
import { Image } from '../domain/snip/models/image.entity';
import { Office } from '../domain/snip/models/office.entity';
import { OtherWebpage } from '../domain/snip/models/other-webpage.entity';
import { PDF } from '../domain/snip/models/pdf.entity';
import {
  BoardItemInfo,
  Snip,
  SnipFrom,
  SnipStatus,
  SnipType,
} from '../domain/snip/models/snip.entity';
import { Snippet } from '../domain/snip/models/snippet.entity';
import { TextFile } from '../domain/snip/models/text-file.entity';
import { HTMLSelection } from '../domain/snip/models/type';
import { UnknownWebpage } from '../domain/snip/models/unknown-webpage.entity';
import { Video } from '../domain/snip/models/video.entity';
import { Voice } from '../domain/snip/models/voice.entity';
import { ReaderHTMLContent } from '../domain/snip/value-objects/content.vo';
import { WebpageMeta } from '../domain/snip/value-objects/webpage-meta.vo';

// 查询过滤器接口
export interface SnipFilter {
  spaceId: string;
  creatorId?: string;
  ids?: string[]; // ID 批量过滤
  type?: SnipType;
  from?: SnipFrom;
  status?: SnipStatus;
  visibility?: Visibility;
  parentId?: string;
  boardId?: string;
  webpageUrl?: string;
  webpageNormalizedUrl?: string;
  webpageNormalizedUrls?: string[]; // 批量 normalized URLs 过滤
  webpageSiteHost?: string; // 站点过滤
  title?: string;
  // 时间范围过滤
  createdBefore?: Date;
  createdAfter?: Date;
  // 分页
  limit?: number;
  offset?: number;
}

// 批量查询参数
export interface BatchQueryParams {
  ids: string[];
  spaceId?: string;
  includeDeleted?: boolean;
}

// 分页参数
export interface PaginationParams {
  limit: number;
  offset: number;
}

// 数据库类型
type SnipDO = typeof snips.$inferSelect;
type BoardItemDO = typeof boardItems.$inferSelect;

@Injectable()
export class SnipRepository {
  private static readonly logger = new Logger(SnipRepository.name);

  constructor(private readonly databaseService: DatabaseService) {}

  private get db() {
    return this.databaseService.db;
  }

  /**
   * 保存 Snip 聚合 - 支持位置管理
   */
  async save(snip: Snip, trx?: any): Promise<void> {
    const snipDO = this.entityToDO(snip);
    const db = trx || this.db;

    SnipRepository.logger.debug(`Saving snip: ${JSON.stringify(snipDO)}`);

    try {
      if (snip.isNew) {
        // 插入新记录
        await db.insert(snips).values(snipDO);

        // 如果有位置信息，创建 board_item 关联
        if (snip.position) {
          const boardItemId = await this.saveBoardItem('snip', snip.id, snip.position, trx);
          snip.updateBoardItemId(boardItemId);
        }

        snip.markAsExisting();
      } else {
        // 更新现有记录
        const { id, ...shouldUpdate } = snipDO;
        await db
          .update(snips)
          .set(shouldUpdate)
          .where(and(isNull(snips.deletedAt), eq(snips.id, id)));

        // 同步 board_item 信息
        if (snip.position) {
          await this.upsertBoardItem('snip', snip.id, snip.position, trx);
        } else {
          // 如果位置被移除，删除 board_item
          await db.delete(boardItems).where(eq(boardItems.snipId, snip.id));
        }
      }
    } catch (error) {
      SnipRepository.logger.error('保存 Snip 失败', error);
      throw error;
    }
  }

  /**
   * 根据 ID 查找 Snip
   */
  async findById(id: string): Promise<Snip | undefined> {
    try {
      const result = await this.db
        .select({
          // snip fields
          id: snips.id,
          createdAt: snips.createdAt,
          updatedAt: snips.updatedAt,
          deletedAt: snips.deletedAt,
          spaceId: snips.spaceId,
          creatorId: snips.creatorId,
          type: snips.type,
          from: snips.from,
          status: snips.status,
          title: snips.title,
          extra: snips.extra,
          visibility: snips.visibility,
          // webpage fields
          webpageUrl: snips.webpageUrl,
          webpageNormalizedUrl: snips.webpageNormalizedUrl,
          webpageTitle: snips.webpageTitle,
          webpageDescription: snips.webpageDescription,
          webpageSiteName: snips.webpageSiteName,
          webpageSiteHost: snips.webpageSiteHost,
          webpageSiteFaviconUrl: snips.webpageSiteFaviconUrl,
          // content fields
          contentFormat: snips.contentFormat,
          contentRaw: snips.contentRaw,
          contentPlain: snips.contentPlain,
          contentLanguage: snips.contentLanguage,
          // file fields
          fileName: snips.fileName,
          fileMimeType: snips.fileMimeType,
          fileSize: snips.fileSize,
          fileStorageUrl: snips.fileStorageUrl,
          fileOriginalUrl: snips.fileOriginalUrl,
          // annotation fields
          annotationRaw: snips.annotationRaw,
          annotationPlain: snips.annotationPlain,
          // parsed fields
          authors: snips.authors,
          heroImageUrl: snips.heroImageUrl,
          publishedAt: snips.publishedAt,
          // selection and other fields
          parentId: snips.parentId,
          selection: snips.selection,
          playUrl: snips.playUrl,
          // board_item fields
          boardItemId: boardItems.id,
          boardItemBoardId: boardItems.boardId,
          boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
          boardItemBoardGroupId: boardItems.boardGroupId,
          boardItemRank: boardItems.rank,
          boardItemCreatedAt: boardItems.createdAt,
          boardItemUpdatedAt: boardItems.updatedAt,
        })
        .from(snips)
        .leftJoin(boardItems, and(eq(boardItems.snipId, snips.id), isNull(boardItems.deletedAt)))
        .where(and(isNull(snips.deletedAt), eq(snips.id, id)));

      if (result.length === 0) {
        return undefined;
      }

      return this.doToEntity(result[0]);
    } catch (error) {
      SnipRepository.logger.error(`根据 ID 查找 Snip 失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 根据 ID 获取 Snip（不存在则抛异常）
   */
  async getById(id: string): Promise<Snip> {
    const snip = await this.findById(id);
    if (!snip) {
      throw new NotFoundException(`Snip with id ${id} not found`);
    }
    return snip;
  }

  /**
   * 根据条件查找 Snip 列表
   */
  async findByFilter(filter: SnipFilter): Promise<Snip[]> {
    try {
      const conditions = [isNull(snips.deletedAt), eq(snips.spaceId, filter.spaceId)];

      if (filter.creatorId) {
        conditions.push(eq(snips.creatorId, filter.creatorId));
      }

      if (filter.ids && filter.ids.length > 0) {
        conditions.push(inArray(snips.id, filter.ids));
      }

      if (filter.type) {
        conditions.push(eq(snips.type, filter.type));
      }

      if (filter.from) {
        conditions.push(eq(snips.from, filter.from));
      }

      if (filter.status) {
        conditions.push(eq(snips.status, filter.status));
      }

      if (filter.visibility) {
        conditions.push(eq(snips.visibility, filter.visibility));
      }

      if (filter.parentId) {
        conditions.push(eq(snips.parentId, filter.parentId));
      }

      if (filter.webpageUrl) {
        conditions.push(eq(snips.webpageUrl, filter.webpageUrl));
      }

      if (filter.webpageNormalizedUrl) {
        conditions.push(eq(snips.webpageNormalizedUrl, filter.webpageNormalizedUrl));
      }
      if (filter.webpageNormalizedUrls && filter.webpageNormalizedUrls.length > 0) {
        conditions.push(inArray(snips.webpageNormalizedUrl, filter.webpageNormalizedUrls));
      }

      if (filter.title) {
        conditions.push(like(snips.title, `%${filter.title}%`));
      }

      if (filter.webpageSiteHost) {
        conditions.push(eq(snips.webpageSiteHost, filter.webpageSiteHost));
      }

      if (filter.createdBefore) {
        conditions.push(lt(snips.createdAt, filter.createdBefore));
      }

      if (filter.createdAfter) {
        conditions.push(gt(snips.createdAt, filter.createdAfter));
      }

      const result = await this.db
        .select({
          // snip fields
          id: snips.id,
          createdAt: snips.createdAt,
          updatedAt: snips.updatedAt,
          deletedAt: snips.deletedAt,
          spaceId: snips.spaceId,
          creatorId: snips.creatorId,
          type: snips.type,
          from: snips.from,
          status: snips.status,
          title: snips.title,
          extra: snips.extra,
          visibility: snips.visibility,
          // webpage fields
          webpageUrl: snips.webpageUrl,
          webpageNormalizedUrl: snips.webpageNormalizedUrl,
          webpageTitle: snips.webpageTitle,
          webpageDescription: snips.webpageDescription,
          webpageSiteName: snips.webpageSiteName,
          webpageSiteHost: snips.webpageSiteHost,
          webpageSiteFaviconUrl: snips.webpageSiteFaviconUrl,
          // content fields
          contentFormat: snips.contentFormat,
          contentRaw: snips.contentRaw,
          contentPlain: snips.contentPlain,
          contentLanguage: snips.contentLanguage,
          // selection and other fields
          parentId: snips.parentId,
          selection: snips.selection,
          playUrl: snips.playUrl,
          // board_item fields
          boardItemId: boardItems.id,
          boardItemBoardId: boardItems.boardId,
          boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
          boardItemBoardGroupId: boardItems.boardGroupId,
          boardItemRank: boardItems.rank,
          boardItemCreatedAt: boardItems.createdAt,
          boardItemUpdatedAt: boardItems.updatedAt,
        })
        .from(snips)
        .leftJoin(boardItems, and(eq(boardItems.snipId, snips.id), isNull(boardItems.deletedAt)))
        .where(and(...conditions))
        .orderBy(desc(snips.createdAt))
        .limit(filter.limit || 100)
        .offset(filter.offset || 0);
      return result.map((row) => this.doToEntity(row));
    } catch (error) {
      SnipRepository.logger.error('根据条件查找 Snip 列表失败', error);
      throw error;
    }
  }

  /**
   * 根据空间 ID 查找所有 Snip
   */
  async findBySpaceId(spaceId: string): Promise<Snip[]> {
    return this.findByFilter({ spaceId });
  }

  /**
   * 根据创建者 ID 查找 Snip
   */
  async findByCreatorId(creatorId: string, spaceId: string): Promise<Snip[]> {
    return this.findByFilter({ spaceId, creatorId });
  }

  /**
   * 根据空间 ID 查找未使用的 Snip (未添加到任何 board 的 Snip)
   * 参考 youapp 的 selectUnused 实现
   */
  async findUnusedBySpaceId(spaceId: string, limit?: number): Promise<Snip[]> {
    try {
      const baseQuery = this.db
        .select({
          // snip fields
          id: snips.id,
          createdAt: snips.createdAt,
          updatedAt: snips.updatedAt,
          deletedAt: snips.deletedAt,
          spaceId: snips.spaceId,
          creatorId: snips.creatorId,
          type: snips.type,
          from: snips.from,
          status: snips.status,
          title: snips.title,
          extra: snips.extra,
          visibility: snips.visibility,
          // webpage fields
          webpageUrl: snips.webpageUrl,
          webpageNormalizedUrl: snips.webpageNormalizedUrl,
          webpageTitle: snips.webpageTitle,
          webpageDescription: snips.webpageDescription,
          webpageSiteName: snips.webpageSiteName,
          webpageSiteHost: snips.webpageSiteHost,
          webpageSiteFaviconUrl: snips.webpageSiteFaviconUrl,
          // content fields
          contentFormat: snips.contentFormat,
          contentRaw: snips.contentRaw,
          contentPlain: snips.contentPlain,
          contentLanguage: snips.contentLanguage,
          // file fields
          fileName: snips.fileName,
          fileMimeType: snips.fileMimeType,
          fileSize: snips.fileSize,
          fileStorageUrl: snips.fileStorageUrl,
          fileOriginalUrl: snips.fileOriginalUrl,
          // annotation fields
          annotationRaw: snips.annotationRaw,
          annotationPlain: snips.annotationPlain,
          // parsed fields
          authors: snips.authors,
          heroImageUrl: snips.heroImageUrl,
          publishedAt: snips.publishedAt,
          // selection and other fields
          parentId: snips.parentId,
          selection: snips.selection,
          playUrl: snips.playUrl,
          // board_item fields - 由于是 LEFT JOIN，这些字段可能为 null
          boardItemId: boardItems.id,
          boardItemBoardId: boardItems.boardId,
          boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
          boardItemBoardGroupId: boardItems.boardGroupId,
          boardItemRank: boardItems.rank,
          boardItemCreatedAt: boardItems.createdAt,
          boardItemUpdatedAt: boardItems.updatedAt,
        })
        .from(snips)
        .leftJoin(boardItems, and(eq(boardItems.snipId, snips.id), isNull(boardItems.deletedAt)))
        .where(
          and(
            isNull(snips.deletedAt),
            eq(snips.spaceId, spaceId),
            isNull(boardItems.id), // 未关联到任何 board_item 的 snip 被认为是 unused
          ),
        )
        .$dynamic();

      // 如果提供了 limit 参数才进行限制，否则返回所有结果（按照原 youapp 逻辑）
      const result = limit !== undefined ? await baseQuery.limit(limit) : await baseQuery;

      return result.map((row) => this.doToEntity(row));
    } catch (error) {
      SnipRepository.logger.error(`根据空间 ID 查找未使用的 Snip 失败: ${spaceId}`, error);
      throw error;
    }
  }

  /**
   * 根据空间 ID 查找进行中的 Snip (在进行中状态的板子里的 Snip)
   * 参考 youapp 的 selectInProgress 实现
   */
  async findInProgressBySpaceId(spaceId: string): Promise<Snip[]> {
    try {
      const result = await this.db
        .select({
          // snip fields
          id: snips.id,
          createdAt: snips.createdAt,
          updatedAt: snips.updatedAt,
          deletedAt: snips.deletedAt,
          spaceId: snips.spaceId,
          creatorId: snips.creatorId,
          type: snips.type,
          from: snips.from,
          status: snips.status,
          title: snips.title,
          extra: snips.extra,
          visibility: snips.visibility,
          // webpage fields
          webpageUrl: snips.webpageUrl,
          webpageNormalizedUrl: snips.webpageNormalizedUrl,
          webpageTitle: snips.webpageTitle,
          webpageDescription: snips.webpageDescription,
          webpageSiteName: snips.webpageSiteName,
          webpageSiteHost: snips.webpageSiteHost,
          webpageSiteFaviconUrl: snips.webpageSiteFaviconUrl,
          // content fields
          contentFormat: snips.contentFormat,
          contentRaw: snips.contentRaw,
          contentPlain: snips.contentPlain,
          contentLanguage: snips.contentLanguage,
          // file fields
          fileName: snips.fileName,
          fileMimeType: snips.fileMimeType,
          fileSize: snips.fileSize,
          fileStorageUrl: snips.fileStorageUrl,
          fileOriginalUrl: snips.fileOriginalUrl,
          // annotation fields
          annotationRaw: snips.annotationRaw,
          annotationPlain: snips.annotationPlain,
          // parsed fields
          authors: snips.authors,
          heroImageUrl: snips.heroImageUrl,
          publishedAt: snips.publishedAt,
          // selection and other fields
          parentId: snips.parentId,
          selection: snips.selection,
          playUrl: snips.playUrl,
          // board_item fields
          boardItemId: boardItems.id,
          boardItemBoardId: boardItems.boardId,
          boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
          boardItemBoardGroupId: boardItems.boardGroupId,
          boardItemRank: boardItems.rank,
          boardItemCreatedAt: boardItems.createdAt,
          boardItemUpdatedAt: boardItems.updatedAt,
        })
        .from(snips)
        .innerJoin(boardItems, and(eq(boardItems.snipId, snips.id), isNull(boardItems.deletedAt)))
        .innerJoin(boards, and(eq(boards.id, boardItems.boardId), isNull(boards.deletedAt)))
        .where(
          and(
            isNull(snips.deletedAt),
            eq(snips.spaceId, spaceId),
            eq(boards.status, BoardStatusEnum.IN_PROGRESS),
          ),
        )
        .orderBy(desc(snips.createdAt));

      return result.map((row) => this.doToEntity(row));
    } catch (error) {
      SnipRepository.logger.error(`根据空间 ID 查找进行中的 Snip 失败: ${spaceId}`, error);
      throw error;
    }
  }

  /**
   * 根据 board ID 查找 Snip
   */
  async findByBoardId(board_id: string): Promise<Snip[]> {
    try {
      const result = await this.db
        .select({
          // snip fields
          id: snips.id,
          createdAt: snips.createdAt,
          updatedAt: snips.updatedAt,
          deletedAt: snips.deletedAt,
          spaceId: snips.spaceId,
          creatorId: snips.creatorId,
          type: snips.type,
          from: snips.from,
          status: snips.status,
          title: snips.title,
          extra: snips.extra,
          visibility: snips.visibility,
          // webpage fields
          webpageUrl: snips.webpageUrl,
          webpageNormalizedUrl: snips.webpageNormalizedUrl,
          webpageTitle: snips.webpageTitle,
          webpageDescription: snips.webpageDescription,
          webpageSiteName: snips.webpageSiteName,
          webpageSiteHost: snips.webpageSiteHost,
          webpageSiteFaviconUrl: snips.webpageSiteFaviconUrl,
          // content fields
          contentFormat: snips.contentFormat,
          contentRaw: snips.contentRaw,
          contentPlain: snips.contentPlain,
          contentLanguage: snips.contentLanguage,
          // file fields
          fileName: snips.fileName,
          fileMimeType: snips.fileMimeType,
          fileSize: snips.fileSize,
          fileStorageUrl: snips.fileStorageUrl,
          fileOriginalUrl: snips.fileOriginalUrl,
          // annotation fields
          annotationRaw: snips.annotationRaw,
          annotationPlain: snips.annotationPlain,
          // parsed fields
          authors: snips.authors,
          heroImageUrl: snips.heroImageUrl,
          publishedAt: snips.publishedAt,
          // selection and other fields
          parentId: snips.parentId,
          selection: snips.selection,
          playUrl: snips.playUrl,
          // board_item fields
          boardItemId: boardItems.id,
          boardItemBoardId: boardItems.boardId,
          boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
          boardItemBoardGroupId: boardItems.boardGroupId,
          boardItemRank: boardItems.rank,
          boardItemCreatedAt: boardItems.createdAt,
          boardItemUpdatedAt: boardItems.updatedAt,
        })
        .from(snips)
        .innerJoin(boardItems, and(eq(boardItems.snipId, snips.id), isNull(boardItems.deletedAt)))
        .where(and(isNull(snips.deletedAt), eq(boardItems.boardId, board_id)))
        .orderBy(boardItems.rank);

      return result.map((row) => this.doToEntity(row));
    } catch (error) {
      SnipRepository.logger.error(`根据 board ID 查找 Snip 失败: ${board_id}`, error);
      throw error;
    }
  }

  /**
   * 根据 boardItemId 查找 Snip
   */
  async findByBoardItemId(boardItemId: string): Promise<Snip | undefined> {
    try {
      const result = await this.db
        .select({
          // snip fields
          id: snips.id,
          createdAt: snips.createdAt,
          updatedAt: snips.updatedAt,
          deletedAt: snips.deletedAt,
          spaceId: snips.spaceId,
          creatorId: snips.creatorId,
          type: snips.type,
          from: snips.from,
          status: snips.status,
          title: snips.title,
          extra: snips.extra,
          visibility: snips.visibility,
          // webpage fields
          webpageUrl: snips.webpageUrl,
          webpageNormalizedUrl: snips.webpageNormalizedUrl,
          webpageTitle: snips.webpageTitle,
          webpageDescription: snips.webpageDescription,
          webpageSiteName: snips.webpageSiteName,
          webpageSiteHost: snips.webpageSiteHost,
          webpageSiteFaviconUrl: snips.webpageSiteFaviconUrl,
          // content fields
          contentFormat: snips.contentFormat,
          contentRaw: snips.contentRaw,
          contentPlain: snips.contentPlain,
          contentLanguage: snips.contentLanguage,
          // file fields
          fileName: snips.fileName,
          fileMimeType: snips.fileMimeType,
          fileSize: snips.fileSize,
          fileStorageUrl: snips.fileStorageUrl,
          fileOriginalUrl: snips.fileOriginalUrl,
          // annotation fields
          annotationRaw: snips.annotationRaw,
          annotationPlain: snips.annotationPlain,
          // parsed fields
          authors: snips.authors,
          heroImageUrl: snips.heroImageUrl,
          publishedAt: snips.publishedAt,
          // selection and other fields
          parentId: snips.parentId,
          selection: snips.selection,
          playUrl: snips.playUrl,
          // board_item fields
          boardItemId: boardItems.id,
          boardItemBoardId: boardItems.boardId,
          boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
          boardItemBoardGroupId: boardItems.boardGroupId,
          boardItemRank: boardItems.rank,
          boardItemCreatedAt: boardItems.createdAt,
          boardItemUpdatedAt: boardItems.updatedAt,
        })
        .from(snips)
        .innerJoin(boardItems, and(eq(boardItems.snipId, snips.id), isNull(boardItems.deletedAt)))
        .where(and(isNull(snips.deletedAt), eq(boardItems.id, boardItemId)));

      if (result.length === 0) {
        return undefined;
      }

      return this.doToEntity(result[0]);
    } catch (error) {
      SnipRepository.logger.error(`根据 boardItemId 查找 Snip 失败: ${boardItemId}`, error);
      throw error;
    }
  }

  /**
   * 检查网页是否已存在
   */
  async isWebpageExists(spaceId: string, normalizedUrl: string): Promise<boolean> {
    try {
      const result = await this.db
        .select({ count: count(snips.id) })
        .from(snips)
        .where(
          and(
            isNull(snips.deletedAt),
            eq(snips.spaceId, spaceId),
            eq(snips.from, SnipFrom.WEBPAGE),
            eq(snips.webpageNormalizedUrl, normalizedUrl),
          ),
        );

      return result[0].count > 0;
    } catch (error) {
      SnipRepository.logger.error(`检查网页是否存在失败: ${normalizedUrl}`, error);
      throw error;
    }
  }

  /**
   * 根据标准化 URL 查找网页
   */
  async findWebpageByNormalizedUrl(
    spaceId: string,
    normalizedUrl: string,
  ): Promise<Snip | undefined> {
    return this.findByFilter({
      spaceId,
      from: SnipFrom.WEBPAGE,
      webpageNormalizedUrl: normalizedUrl,
      limit: 1,
    }).then((results) => results[0]);
  }

  /**
   * 根据 board ID 和 normalized URLs 查找网页片段
   * 纯数据访问方法，不包含业务逻辑
   */
  async findWebpagesInBoardByNormalizedUrls(
    boardId: string,
    normalizedUrls: string[],
    spaceId: string,
  ): Promise<Snip[]> {
    try {
      if (normalizedUrls.length === 0) {
        return [];
      }

      const result = await this.db
        .select({
          // snip fields
          id: snips.id,
          createdAt: snips.createdAt,
          updatedAt: snips.updatedAt,
          deletedAt: snips.deletedAt,
          spaceId: snips.spaceId,
          creatorId: snips.creatorId,
          type: snips.type,
          from: snips.from,
          status: snips.status,
          title: snips.title,
          extra: snips.extra,
          visibility: snips.visibility,
          // webpage fields
          webpageUrl: snips.webpageUrl,
          webpageNormalizedUrl: snips.webpageNormalizedUrl,
          webpageTitle: snips.webpageTitle,
          webpageDescription: snips.webpageDescription,
          webpageSiteName: snips.webpageSiteName,
          webpageSiteHost: snips.webpageSiteHost,
          webpageSiteFaviconUrl: snips.webpageSiteFaviconUrl,
          // content fields
          contentFormat: snips.contentFormat,
          contentRaw: snips.contentRaw,
          contentPlain: snips.contentPlain,
          contentLanguage: snips.contentLanguage,
          // file fields
          fileName: snips.fileName,
          fileMimeType: snips.fileMimeType,
          fileSize: snips.fileSize,
          fileStorageUrl: snips.fileStorageUrl,
          fileOriginalUrl: snips.fileOriginalUrl,
          // additional fields
          parentId: snips.parentId,
          selection: snips.selection,
          annotationRaw: snips.annotationRaw,
          annotationPlain: snips.annotationPlain,
          playUrl: snips.playUrl,
          heroImageUrl: snips.heroImageUrl,
          publishedAt: snips.publishedAt,
          authors: snips.authors,
          // board_item fields
          boardItemId: boardItems.id,
          boardItemBoardId: boardItems.boardId,
          boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
          boardItemBoardGroupId: boardItems.boardGroupId,
          boardItemRank: boardItems.rank,
          boardItemCreatedAt: boardItems.createdAt,
          boardItemUpdatedAt: boardItems.updatedAt,
        })
        .from(snips)
        .innerJoin(
          boardItems,
          and(
            isNull(boardItems.deletedAt),
            eq(boardItems.boardId, boardId),
            eq(snips.id, boardItems.snipId),
          ),
        )
        .where(
          and(
            isNull(snips.deletedAt),
            eq(snips.spaceId, spaceId),
            eq(snips.from, 'webpage'),
            inArray(snips.webpageNormalizedUrl, normalizedUrls),
          ),
        )
        .orderBy(desc(snips.createdAt));

      return result.map((row) => this.doToEntity(row));
    } catch (error) {
      SnipRepository.logger.error(
        `根据 board ID 和 normalized URLs 查找网页片段失败: ${boardId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * 批量查找 Snip
   */
  /**
   * 批量查询 Snip - 内容有裁剪，仅 SNIPPET 类型返回完整内容
   * 其他类型（Article、Image、Video、PDF等）的 content 字段为空，以优化性能
   */
  async findByIdsWithContentTruncated(params: BatchQueryParams): Promise<Snip[]> {
    if (params.ids.length === 0) {
      return [];
    }

    const conditions = [inArray(snips.id, params.ids)];

    if (!params.includeDeleted) {
      conditions.push(isNull(snips.deletedAt));
    }

    if (params.spaceId) {
      conditions.push(eq(snips.spaceId, params.spaceId));
    }

    const result = await this.db
      .select({
        // snip fields
        id: snips.id,
        createdAt: snips.createdAt,
        updatedAt: snips.updatedAt,
        deletedAt: snips.deletedAt,
        spaceId: snips.spaceId,
        creatorId: snips.creatorId,
        type: snips.type,
        from: snips.from,
        status: snips.status,
        title: snips.title,
        extra: snips.extra,
        visibility: snips.visibility,
        // webpage fields
        webpageUrl: snips.webpageUrl,
        webpageNormalizedUrl: snips.webpageNormalizedUrl,
        webpageTitle: snips.webpageTitle,
        webpageDescription: snips.webpageDescription,
        webpageSiteName: snips.webpageSiteName,
        webpageSiteHost: snips.webpageSiteHost,
        webpageSiteFaviconUrl: snips.webpageSiteFaviconUrl,
        // content fields - 仅对 SNIPPET 类型加载完整内容
        contentFormat: snips.contentFormat,
        contentRaw: sql<string>`
          CASE ${snips.type}
            WHEN ${SnipType.SNIPPET} THEN ${snips.contentRaw}
            ELSE ''
          END
        `,
        contentPlain: sql<string>`
          CASE ${snips.type}
            WHEN ${SnipType.SNIPPET} THEN ${snips.contentPlain}
            ELSE ''
          END
        `,
        contentLanguage: snips.contentLanguage,
        // file fields
        fileName: snips.fileName,
        fileMimeType: snips.fileMimeType,
        fileSize: snips.fileSize,
        fileStorageUrl: snips.fileStorageUrl,
        fileOriginalUrl: snips.fileOriginalUrl,
        // annotation fields - 不进行截取，保持原样
        annotationRaw: snips.annotationRaw,
        annotationPlain: snips.annotationPlain,
        // parsed fields
        authors: snips.authors,
        heroImageUrl: snips.heroImageUrl,
        publishedAt: snips.publishedAt,
        // selection and other fields
        parentId: snips.parentId,
        selection: snips.selection,
        playUrl: snips.playUrl,
        // board_item fields
        boardItemId: boardItems.id,
        boardItemBoardId: boardItems.boardId,
        boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
        boardItemBoardGroupId: boardItems.boardGroupId,
        boardItemRank: boardItems.rank,
        boardItemCreatedAt: boardItems.createdAt,
        boardItemUpdatedAt: boardItems.updatedAt,
      })
      .from(snips)
      .leftJoin(boardItems, and(eq(boardItems.snipId, snips.id), isNull(boardItems.deletedAt)))
      .where(and(...conditions));

    return result.map((row) => this.doToEntity(row));
  }

  async findByIds(params: BatchQueryParams): Promise<Snip[]> {
    try {
      if (params.ids.length === 0) {
        return [];
      }

      const conditions = [inArray(snips.id, params.ids)];

      if (!params.includeDeleted) {
        conditions.push(isNull(snips.deletedAt));
      }

      if (params.spaceId) {
        conditions.push(eq(snips.spaceId, params.spaceId));
      }

      const result = await this.db
        .select({
          // snip fields
          id: snips.id,
          createdAt: snips.createdAt,
          updatedAt: snips.updatedAt,
          deletedAt: snips.deletedAt,
          spaceId: snips.spaceId,
          creatorId: snips.creatorId,
          type: snips.type,
          from: snips.from,
          status: snips.status,
          title: snips.title,
          extra: snips.extra,
          visibility: snips.visibility,
          // webpage fields
          webpageUrl: snips.webpageUrl,
          webpageNormalizedUrl: snips.webpageNormalizedUrl,
          webpageTitle: snips.webpageTitle,
          webpageDescription: snips.webpageDescription,
          webpageSiteName: snips.webpageSiteName,
          webpageSiteHost: snips.webpageSiteHost,
          webpageSiteFaviconUrl: snips.webpageSiteFaviconUrl,
          // content fields
          contentFormat: snips.contentFormat,
          contentRaw: snips.contentRaw,
          contentPlain: snips.contentPlain,
          contentLanguage: snips.contentLanguage,
          // file fields
          fileName: snips.fileName,
          fileMimeType: snips.fileMimeType,
          fileSize: snips.fileSize,
          fileStorageUrl: snips.fileStorageUrl,
          fileOriginalUrl: snips.fileOriginalUrl,
          // annotation fields
          annotationRaw: snips.annotationRaw,
          annotationPlain: snips.annotationPlain,
          // parsed fields
          authors: snips.authors,
          heroImageUrl: snips.heroImageUrl,
          publishedAt: snips.publishedAt,
          // selection and other fields
          parentId: snips.parentId,
          selection: snips.selection,
          playUrl: snips.playUrl,
          // board_item fields
          boardItemId: boardItems.id,
          boardItemBoardId: boardItems.boardId,
          boardItemParentBoardGroupId: boardItems.parentBoardGroupId,
          boardItemBoardGroupId: boardItems.boardGroupId,
          boardItemRank: boardItems.rank,
          boardItemCreatedAt: boardItems.createdAt,
          boardItemUpdatedAt: boardItems.updatedAt,
        })
        .from(snips)
        .leftJoin(boardItems, and(eq(boardItems.snipId, snips.id), isNull(boardItems.deletedAt)))
        .where(and(...conditions));

      return result.map((row) => this.doToEntity(row));
    } catch (error) {
      SnipRepository.logger.error('批量查找 Snip 失败', error);
      throw error;
    }
  }

  /**
   * 统计 Snip 数量
   */
  async countByFilter(filter: SnipFilter): Promise<number> {
    try {
      const conditions = [isNull(snips.deletedAt), eq(snips.spaceId, filter.spaceId)];

      if (filter.creatorId) {
        conditions.push(eq(snips.creatorId, filter.creatorId));
      }

      if (filter.ids && filter.ids.length > 0) {
        conditions.push(inArray(snips.id, filter.ids));
      }

      if (filter.type) {
        conditions.push(eq(snips.type, filter.type));
      }

      if (filter.from) {
        conditions.push(eq(snips.from, filter.from));
      }

      if (filter.status) {
        conditions.push(eq(snips.status, filter.status));
      }

      if (filter.visibility) {
        conditions.push(eq(snips.visibility, filter.visibility));
      }

      if (filter.webpageSiteHost) {
        conditions.push(eq(snips.webpageSiteHost, filter.webpageSiteHost));
      }

      if (filter.createdBefore) {
        conditions.push(lt(snips.createdAt, filter.createdBefore));
      }

      if (filter.createdAfter) {
        conditions.push(gt(snips.createdAt, filter.createdAfter));
      }

      const result = await this.db
        .select({ count: count(snips.id) })
        .from(snips)
        .where(and(...conditions));

      return result[0].count;
    } catch (error) {
      SnipRepository.logger.error('统计 Snip 数量失败', error);
      throw error;
    }
  }

  /**
   * 删除 Snip（软删除）
   */
  async deleteById(id: string): Promise<void> {
    try {
      const now = new Date();

      // 1. 软删除 Snip 记录
      await this.db
        .update(snips)
        .set({
          deletedAt: now,
          updatedAt: now,
        } as any)
        .where(and(isNull(snips.deletedAt), eq(snips.id, id)));

      // 2. 软删除关联的 boardItems 记录
      await this.db
        .update(boardItems)
        .set({
          deletedAt: now,
          updatedAt: now,
        } as any)
        .where(and(isNull(boardItems.deletedAt), eq(boardItems.snipId, id)));
    } catch (error) {
      SnipRepository.logger.error(`删除 Snip 失败: ${id}`, error);
      throw error;
    }
  }

  /**
   * 批量删除 Snip（软删除）
   */
  async deleteManyByIds(ids: string[]): Promise<void> {
    try {
      if (ids.length === 0) {
        return;
      }

      const now = new Date();

      // 1. 软删除 Snip 记录
      await this.db
        .update(snips)
        .set({
          deletedAt: now,
          updatedAt: now,
        } as any)
        .where(and(isNull(snips.deletedAt), inArray(snips.id, ids)));

      // 2. 软删除关联的 boardItems 记录
      await this.db
        .update(boardItems)
        .set({
          deletedAt: now,
          updatedAt: now,
        } as any)
        .where(and(isNull(boardItems.deletedAt), inArray(boardItems.snipId, ids)));
    } catch (error) {
      SnipRepository.logger.error('批量删除 Snip 失败', error);
      throw error;
    }
  }

  /**
   * 实体转换为 DO
   */
  private entityToDO(snip: Snip): SnipDO {
    // 基础字段
    const baseDO = {
      id: snip.id,
      createdAt: snip.createdAt,
      updatedAt: snip.updatedAt,
      deletedAt: null, // 软删除字段由仓储层控制
      spaceId: snip.spaceId,
      creatorId: snip.creatorId,
      type: snip.type,
      from: snip.from,
      status: snip.status || null,
      title: snip.title || null,
      extra: snip.extra || null,
      visibility: snip.visibility,
      parentId: snip.parentId || null,
      // file fields (暂时设为 null)
      fileName: null,
      fileMimeType: null,
      fileSize: null,
      fileStorageUrl: null,
      fileOriginalUrl: null,
      // annotation fields (暂时设为 null)
      annotationRaw: null,
      annotationPlain: null,
      // parsed fields (暂时设为 null)
      authors: null,
      heroImageUrl: null,
      publishedAt: null,
    };

    // 对于 Snippet 类型，添加特有字段
    if (snip instanceof Snippet) {
      const snippet = snip as Snippet;
      return {
        ...baseDO,
        // webpage fields
        webpageUrl: snippet.webpage?.getUrl() || null,
        webpageNormalizedUrl: snippet.webpage?.getNormalizedUrl() || null,

        webpageTitle: snippet.webpage?.getTitle() || null,
        webpageDescription: snippet.webpage?.getDescription() || null,
        webpageSiteName: snippet.webpage?.getSite()?.name || null,
        webpageSiteHost: snippet.webpage?.getSite()?.host || null,
        webpageSiteFaviconUrl: snippet.webpage?.getSite()?.faviconUrl || null,
        // content fields
        contentFormat: snippet.content?.getFormat() || null,
        contentRaw: snippet.content?.getRaw() || null,
        contentPlain: snippet.content?.getPlain() || null,
        contentLanguage: null, // TODO: 需要从 content 中提取
        // selection and other fields
        selection: snippet.selection ? JSON.stringify(snippet.selection) : null,
        playUrl: snippet.playUrl || null,
      };
    } else if (snip instanceof Article) {
      // 对于 Article 类型，添加特有字段
      const article = snip as Article;
      return {
        ...baseDO,
        // webpage fields
        webpageUrl: article.webpage?.getUrl() || null,
        webpageNormalizedUrl: article.webpage?.getNormalizedUrl() || null,
        webpageTitle: article.webpage?.getTitle() || null,
        webpageDescription: article.webpage?.getDescription() || null,
        webpageSiteName: article.webpage?.getSite()?.name || null,
        webpageSiteHost: article.webpage?.getSite()?.host || null,
        webpageSiteFaviconUrl: article.webpage?.getSite()?.faviconUrl || null,
        // content fields
        contentFormat: article.content.getFormat(),
        contentRaw: article.content.getRaw(),
        contentPlain: article.content.getPlain() || null,
        contentLanguage: article.content.getLanguage() || null,
        // article-specific fields
        playUrl: article.playUrl || null,
        authors: article.authors || null,
        heroImageUrl: article.heroImageUrl || null,
        publishedAt: article.publishedAt || null,
        // selection field (not applicable for article)
        selection: null,
      };
    } else if (snip instanceof Image) {
      // 对于 Image 类型，添加特有字段
      const image = snip as Image;
      return {
        ...baseDO,
        // webpage fields
        webpageUrl: image.webpage?.getUrl() || null,
        webpageNormalizedUrl: image.webpage?.getNormalizedUrl() || null,
        webpageTitle: image.webpage?.getTitle() || null,
        webpageDescription: image.webpage?.getDescription() || null,
        webpageSiteName: image.webpage?.getSite()?.name || null,
        webpageSiteHost: image.webpage?.getSite()?.host || null,
        webpageSiteFaviconUrl: image.webpage?.getSite()?.faviconUrl || null,
        // content fields
        fileName: 'name' in image.file ? image.file.name : null,
        fileMimeType: 'mimeType' in image.file ? image.file.mimeType : null,
        fileSize: 'size' in image.file ? image.file.size : null,
        fileStorageUrl: 'storageUrl' in image.file ? image.file.storageUrl : null,
        fileOriginalUrl: 'originalUrl' in image.file ? image.file.originalUrl : null,
        // annotation fields (map description to annotation)
        annotationRaw: image.description || null,
        annotationPlain: null,
        // content fields (map extractedText to content)
        contentFormat: null,
        contentRaw: image.extractedText || null,
        contentPlain: null,
        contentLanguage: null,
        // parsed fields
        authors: null,
        heroImageUrl: null,
        publishedAt: null,
        // selection field (not applicable for image)
        selection: null,
        playUrl: null,
      };
    } else if (snip instanceof Office) {
      // 对于 Office 类型，添加特有字段
      const office = snip as Office;
      return {
        ...baseDO,
        // webpage fields (not applicable for office)
        webpageUrl: null,
        webpageNormalizedUrl: null,
        webpageTitle: null,
        webpageDescription: null,
        webpageSiteName: null,
        webpageSiteHost: null,
        webpageSiteFaviconUrl: null,
        // content fields
        contentFormat: office.content?.getFormat() || null,
        contentRaw: office.content?.getRaw() || null,
        contentPlain: office.content?.getPlain() || null,
        contentLanguage: office.content?.getLanguage() || null,
        // file fields
        fileName: office.file.name,
        fileMimeType: office.file.mimeType,
        fileSize: office.file.size,
        fileStorageUrl: office.file.storageUrl,
        fileOriginalUrl: null,
        // office-specific fields
        playUrl: null,
        authors: office.authors || null,
        heroImageUrl: office.heroImageUrl || null,
        publishedAt: office.publishedAt || null,
        // selection field (not applicable for office)
        selection: null,
        // annotation fields (not applicable for office)
        annotationRaw: null,
        annotationPlain: null,
      };
    } else if (snip instanceof Video) {
      // 对于 Video 类型，添加特有字段
      const video = snip as Video;
      return {
        ...baseDO,
        // webpage fields
        webpageUrl: video.webpage.getUrl(),
        webpageNormalizedUrl: video.webpage.getNormalizedUrl(),
        webpageTitle: video.webpage.getTitle(),
        webpageDescription: video.webpage.getDescription(),
        webpageSiteName: video.webpage.getSite().name,
        webpageSiteHost: video.webpage.getSite().host,
        webpageSiteFaviconUrl: video.webpage.getSite().faviconUrl,
        // content fields
        contentFormat: video.description.getFormat(),
        contentRaw: video.description.getRaw(),
        contentPlain: video.description.getPlain() || null,
        contentLanguage: video.description.getLanguage() || null,
        // video-specific fields
        playUrl: video.playUrl || null,
        authors: video.authors || null,
        heroImageUrl: video.heroImageUrl || null,
        publishedAt: video.publishedAt || null,
        // selection field (not applicable for video)
        selection: null,
      };
    } else if (snip instanceof PDF) {
      // 对于 PDF 类型，添加特有字段
      const pdf = snip as PDF;
      return {
        ...baseDO,
        // webpage fields (not applicable for PDF)
        webpageUrl: null,
        webpageNormalizedUrl: null,
        webpageTitle: null,
        webpageDescription: null,
        webpageSiteName: null,
        webpageSiteHost: null,
        webpageSiteFaviconUrl: null,
        // content fields
        contentFormat: pdf.content?.getFormat() || null,
        contentRaw: pdf.content?.getRaw() || null,
        contentPlain: pdf.content?.getPlain() || null,
        contentLanguage: pdf.content?.getLanguage() || null,
        // file fields
        fileName: pdf.file.name,
        fileMimeType: pdf.file.mimeType,
        fileSize: pdf.file.size,
        fileStorageUrl: pdf.file.storageUrl,
        fileOriginalUrl: null,
        // PDF-specific fields
        playUrl: null,
        authors: pdf.authors || null,
        heroImageUrl: pdf.heroImageUrl || null,
        publishedAt: pdf.publishedAt || null,
        // selection field (not applicable for PDF)
        selection: null,
        // annotation fields (not applicable for PDF)
        annotationRaw: null,
        annotationPlain: null,
      };
    } else if (snip instanceof TextFile) {
      // 对于 TextFile 类型，添加特有字段
      const textFile = snip as TextFile;
      return {
        ...baseDO,
        // webpage fields (not applicable for TextFile)
        webpageUrl: null,
        webpageNormalizedUrl: null,
        webpageTitle: null,
        webpageDescription: null,
        webpageSiteName: null,
        webpageSiteHost: null,
        webpageSiteFaviconUrl: null,
        // content fields
        contentFormat: textFile.content.getFormat(),
        contentRaw: textFile.content.getRaw(),
        contentPlain: textFile.content.getPlain() || null,
        contentLanguage: textFile.content.getLanguage() || null,
        // file fields
        fileName: textFile.file.name,
        fileMimeType: textFile.file.mimeType,
        fileSize: textFile.file.size,
        fileStorageUrl: textFile.file.storageUrl,
        fileOriginalUrl: null,
        // TextFile-specific fields
        playUrl: null,
        authors: textFile.authors || null,
        heroImageUrl: textFile.heroImageUrl || null,
        publishedAt: textFile.publishedAt || null,
        // selection field (not applicable for TextFile)
        selection: null,
        // annotation fields (not applicable for TextFile)
        annotationRaw: null,
        annotationPlain: null,
      };
    } else if (snip instanceof Voice) {
      // 对于 Voice 类型，添加特有字段
      const voice = snip as Voice;
      return {
        ...baseDO,
        // webpage fields (可能有，也可能没有)
        webpageUrl: voice.webpage?.getUrl() || null,
        webpageNormalizedUrl: voice.webpage?.getNormalizedUrl() || null,
        webpageTitle: voice.webpage?.getTitle() || null,
        webpageDescription: voice.webpage?.getDescription() || null,
        webpageSiteName: voice.webpage?.getSite()?.name || null,
        webpageSiteHost: voice.webpage?.getSite()?.host || null,
        webpageSiteFaviconUrl: voice.webpage?.getSite()?.faviconUrl || null,
        // content fields (show notes)
        contentFormat: voice.showNotes?.getFormat() || null,
        contentRaw: voice.showNotes?.getRaw() || null,
        contentPlain: voice.showNotes?.getPlain() || null,
        contentLanguage: voice.showNotes?.getLanguage() || null,
        // file fields (暂时设为 null，Voice 主要是 URL 播放)
        fileName: null,
        fileMimeType: null,
        fileSize: null,
        fileStorageUrl: null,
        fileOriginalUrl: null,
        // Voice-specific fields
        playUrl: voice.playUrl || null,
        authors: voice.authors || null,
        heroImageUrl: voice.heroImageUrl || null,
        publishedAt: voice.publishedAt || null,
        // selection field (not applicable for Voice)
        selection: null,
        // annotation fields (not applicable for Voice)
        annotationRaw: null,
        annotationPlain: null,
      };
    } else if (snip instanceof OtherWebpage) {
      // 对于 OtherWebpage 类型，添加特有字段
      const otherWebpage = snip as OtherWebpage;
      return {
        ...baseDO,
        // webpage fields
        webpageUrl: otherWebpage.webpage.getUrl(),
        webpageNormalizedUrl: otherWebpage.webpage.getNormalizedUrl(),
        webpageTitle: otherWebpage.webpage.getTitle(),
        webpageDescription: otherWebpage.webpage.getDescription(),
        webpageSiteName: otherWebpage.webpage.getSite().name,
        webpageSiteHost: otherWebpage.webpage.getSite().host,
        webpageSiteFaviconUrl: otherWebpage.webpage.getSite().faviconUrl,
        // content fields (not applicable for OtherWebpage)
        contentFormat: null,
        contentRaw: null,
        contentPlain: null,
        contentLanguage: null,
        // file fields (not applicable for OtherWebpage)
        fileName: null,
        fileMimeType: null,
        fileSize: null,
        fileStorageUrl: null,
        fileOriginalUrl: null,
        // parsed fields (not applicable for OtherWebpage)
        playUrl: null,
        authors: null,
        heroImageUrl: null,
        publishedAt: null,
        // selection field (not applicable for OtherWebpage)
        selection: null,
        // annotation fields (not applicable for OtherWebpage)
        annotationRaw: null,
        annotationPlain: null,
      };
    } else if (snip instanceof UnknownWebpage) {
      // 对于 UnknownWebpage 类型，添加特有字段
      const unknownWebpage = snip as UnknownWebpage;
      return {
        ...baseDO,
        // webpage fields
        webpageUrl: unknownWebpage.webpage.getUrl(),
        webpageNormalizedUrl: unknownWebpage.webpage.getNormalizedUrl(),
        webpageTitle: unknownWebpage.webpage.getTitle(),
        webpageDescription: unknownWebpage.webpage.getDescription(),
        webpageSiteName: unknownWebpage.webpage.getSite().name,
        webpageSiteHost: unknownWebpage.webpage.getSite().host,
        webpageSiteFaviconUrl: unknownWebpage.webpage.getSite().faviconUrl,
        // content fields (not applicable for UnknownWebpage)
        contentFormat: null,
        contentRaw: null,
        contentPlain: null,
        contentLanguage: null,
        // file fields (not applicable for UnknownWebpage)
        fileName: null,
        fileMimeType: null,
        fileSize: null,
        fileStorageUrl: null,
        fileOriginalUrl: null,
        // parsed fields (not applicable for UnknownWebpage)
        playUrl: null,
        authors: null,
        heroImageUrl: null,
        publishedAt: null,
        // selection field (not applicable for UnknownWebpage)
        selection: null,
        // annotation fields (not applicable for UnknownWebpage)
        annotationRaw: null,
        annotationPlain: null,
      };
    } else {
      // 对于基础 Snip 类型，设置为 null
      return {
        ...baseDO,
        // webpage fields
        webpageUrl: null,
        webpageNormalizedUrl: null,
        webpageTitle: null,
        webpageDescription: null,
        webpageSiteName: null,
        webpageSiteHost: null,
        webpageSiteFaviconUrl: null,
        // content fields
        contentFormat: null,
        contentRaw: null,
        contentPlain: null,
        contentLanguage: null,
        // selection and other fields
        selection: null,
        playUrl: null,
      };
    }
  }

  /**
   * DO 转换为实体
   */
  private doToEntity(row: any): Snip {
    // 构建 webpage 信息（用于所有实体）
    const webpage: WebpageMeta | undefined = row.webpageUrl
      ? new WebpageMeta(
          row.webpageUrl,
          row.webpageNormalizedUrl || row.webpageUrl,
          row.webpageTitle || '',
          row.webpageDescription || '',
          {
            name: row.webpageSiteName || '',
            host: row.webpageSiteHost || '',
            faviconUrl: row.webpageSiteFaviconUrl || '',
          },
        )
      : undefined;

    // 构建 content 信息（用于 Snippet 等需要 value object 的实体）
    const content: ReaderHTMLContent | undefined = row.contentRaw
      ? new ReaderHTMLContent(
          row.contentRaw,
          row.contentPlain || undefined,
          row.contentLanguage || undefined,
        )
      : undefined;

    // 构建 content entity（用于 Video 等新式实体）
    const contentEntity: ReaderHTMLContent | undefined = row.contentRaw
      ? new ReaderHTMLContent(
          row.contentRaw,
          row.contentPlain || undefined,
          row.contentLanguage || undefined,
        )
      : undefined;

    // 构建 selection 信息
    const selection: HTMLSelection | undefined = row.selection
      ? SafeParse(row.selection, false, {})
      : undefined;

    // 构建位置信息
    const position: BoardPositionInfo | undefined = row.boardItemId
      ? {
          boardId: row.boardItemBoardId,
          rank: row.boardItemRank,
          parentBoardGroupId: row.boardItemParentBoardGroupId,
          boardItemId: row.boardItemId,
        }
      : undefined;

    // 构建 board item 信息用于 API 兼容性
    const boardItemInfo: BoardItemInfo | undefined = row.boardItemId
      ? {
          id: row.boardItemId,
          boardId: row.boardItemBoardId,
          parentBoardGroupId: row.boardItemParentBoardGroupId,
          boardGroupId: row.boardItemBoardGroupId,
          rank: row.boardItemRank,
          createdAt: row.boardItemCreatedAt,
          updatedAt: row.boardItemUpdatedAt,
        }
      : undefined;

    // 根据类型创建不同的实体
    if (row.type === SnipType.SNIPPET && webpage && content && selection) {
      // 创建 Snippet 实体
      const snippet = new Snippet(
        row.id,
        row.createdAt,
        row.updatedAt,
        row.creatorId,
        row.spaceId,
        row.parentId,
        row.title || '',
        row.extra,
        row.status as SnipStatus,
        row.visibility as Visibility,
        webpage,
        content,
        selection,
        boardItemInfo?.boardId,
        boardItemInfo,
        position,
        row.playUrl || undefined,
      );

      snippet.markAsExisting();
      return snippet;
    } else if (row.type === SnipType.ARTICLE && contentEntity) {
      // 创建 Article 实体
      const article = new Article(
        row.id,
        row.createdAt,
        row.updatedAt,
        row.creatorId,
        row.spaceId,
        row.parentId,
        row.title || '',
        row.extra,
        row.status as SnipStatus,
        row.visibility as Visibility,
        contentEntity,
        boardItemInfo?.boardId,
        webpage,
        row.publishedAt || undefined,
        row.playUrl || undefined,
        row.heroImageUrl || undefined,
        row.authors || undefined,
        position,
      );

      article.markAsExisting();
      return article;
    } else if (row.type === SnipType.IMAGE) {
      // 创建 Image 实体
      // 构建文件元信息
      let file: any;

      if (row.fileStorageUrl && row.fileOriginalUrl) {
        // 已转存文件
        file = {
          originalUrl: row.fileOriginalUrl,
          storageUrl: row.fileStorageUrl,
          name: row.fileName || 'image',
          mimeType: row.fileMimeType || 'image/jpeg',
          size: row.fileSize || 0,
        };
      } else if (row.fileStorageUrl) {
        // 上传文件
        file = {
          storageUrl: row.fileStorageUrl,
          name: row.fileName || 'image',
          mimeType: row.fileMimeType || 'image/jpeg',
          size: row.fileSize || 0,
        };
      } else if (row.fileOriginalUrl) {
        // 未转存文件
        file = {
          originalUrl: row.fileOriginalUrl,
        };
      } else {
        throw new Error('Invalid image data: missing file information');
      }

      const image = new Image(
        row.id,
        row.createdAt,
        row.updatedAt,
        row.creatorId,
        row.spaceId,
        row.parentId,
        row.title || '',
        row.extra,
        row.status as SnipStatus,
        row.visibility as Visibility,
        file,
        boardItemInfo?.boardId,
        boardItemInfo,
        position,
        webpage,
        row.annotationRaw || undefined, // maps to description
        row.contentRaw || undefined, // maps to extractedText
      );

      image.markAsExisting();
      return image;
    } else if (row.type === SnipType.OFFICE) {
      // 创建 Office 实体
      // 构建文件元信息
      if (!row.fileStorageUrl || !row.fileName) {
        throw new Error('Invalid office data: missing file information');
      }

      const file = {
        name: row.fileName,
        mimeType: row.fileMimeType || 'application/octet-stream',
        size: row.fileSize || 0,
        storageUrl: row.fileStorageUrl,
      };

      const office = new Office(
        row.id,
        row.createdAt,
        row.updatedAt,
        row.creatorId,
        row.spaceId,
        row.parentId,
        row.title || '',
        row.extra,
        row.status as SnipStatus,
        row.visibility as Visibility,
        file,
        boardItemInfo?.boardId,
        row.publishedAt || undefined,
        row.heroImageUrl || undefined,
        row.authors || undefined,
        contentEntity,
        position,
      );

      office.markAsExisting();
      return office;
    } else if (row.type === SnipType.VIDEO && webpage && contentEntity) {
      // 创建 Video 实体
      const video = new Video(
        row.id,
        row.createdAt,
        row.updatedAt,
        row.creatorId,
        row.spaceId,
        row.parentId,
        row.title || '',
        row.extra,
        row.status as SnipStatus,
        row.visibility as Visibility,
        webpage,
        contentEntity,
        boardItemInfo?.boardId,
        row.playUrl || undefined,
        row.publishedAt || undefined,
        row.heroImageUrl || undefined,
        row.authors || undefined,
        position,
      );

      video.markAsExisting();
      return video;
    } else if (row.type === SnipType.OTHER_WEBPAGE && webpage) {
      // 创建 OtherWebpage 实体
      const otherWebpage = new OtherWebpage({
        id: row.id,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
        creatorId: row.creatorId,
        spaceId: row.spaceId,
        parentId: row.parentId,
        title: row.title || '',
        extra: row.extra,
        status: row.status as SnipStatus,
        visibility: row.visibility as Visibility,
        webpage,
        boardId: boardItemInfo?.boardId,
        boardItemInfo,
        position,
      });

      otherWebpage.markAsExisting();
      return otherWebpage;
    } else if (row.type === SnipType.UNKNOWN_WEBPAGE && webpage) {
      // 创建 UnknownWebpage 实体
      const unknownWebpage = new UnknownWebpage({
        id: row.id,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
        creatorId: row.creatorId,
        spaceId: row.spaceId,
        title: row.title || '',
        extra: row.extra,
        status: row.status as SnipStatus, // 状态是必需的
        visibility: row.visibility as Visibility,
        webpage,
        boardId: boardItemInfo?.boardId,
        boardItemInfo,
        position,
      });

      unknownWebpage.markAsExisting();
      return unknownWebpage;
    } else if (row.type === SnipType.VOICE) {
      // 创建 Voice 实体
      const voice = new Voice({
        id: row.id,
        createdAt: row.createdAt,
        updatedAt: row.updatedAt,
        creatorId: row.creatorId,
        spaceId: row.spaceId,
        parentId: row.parentId,
        title: row.title || '',
        extra: row.extra,
        status: row.status as SnipStatus,
        visibility: row.visibility as Visibility,
        boardId: boardItemInfo?.boardId,
        boardItemInfo,
        position,
        playUrl: row.playUrl,
        showNotes: content,
        webpage,
        publishedAt: row.publishedAt,
        heroImageUrl: row.heroImageUrl,
        authors: row.authors, // author 在数据库中是 jsonb 格式，无需再次转换
      });
      voice.markAsExisting();
      return voice;
    } else if (row.type === SnipType.PDF) {
      // 创建 PDF 实体
      // 构建文件元信息
      if (!row.fileStorageUrl || !row.fileName) {
        throw new Error('Invalid PDF data: missing file information');
      }

      const file = {
        name: row.fileName,
        mimeType: row.fileMimeType || 'application/pdf',
        size: row.fileSize || 0,
        storageUrl: row.fileStorageUrl,
      };

      const pdf = new PDF(
        row.id,
        row.createdAt,
        row.updatedAt,
        row.creatorId,
        row.spaceId,
        row.parentId,
        row.title || '',
        row.extra,
        row.status as SnipStatus,
        row.visibility as Visibility,
        file,
        boardItemInfo?.boardId,
        boardItemInfo,
        position,
        row.publishedAt || undefined,
        row.heroImageUrl || undefined,
        row.authors || undefined,
        contentEntity,
      );

      pdf.markAsExisting();
      return pdf;
    } else if (row.type === SnipType.TEXT_FILE) {
      // 创建 TextFile 实体
      // 构建文件元信息
      if (!row.fileStorageUrl || !row.fileName) {
        throw new Error('Invalid TextFile data: missing file information');
      }

      const file = {
        name: row.fileName,
        mimeType: row.fileMimeType || 'text/plain',
        size: row.fileSize || 0,
        storageUrl: row.fileStorageUrl,
      };

      // TextFile 需要内容
      if (!contentEntity) {
        throw new Error('Invalid TextFile data: missing content');
      }

      const textFile = new TextFile(
        row.id,
        row.createdAt,
        row.updatedAt,
        row.creatorId,
        row.spaceId,
        row.parentId,
        row.title || '',
        row.extra,
        row.status as SnipStatus,
        row.visibility as Visibility,
        file,
        contentEntity,
        boardItemInfo?.boardId,
        boardItemInfo,
        position,
        row.publishedAt || undefined,
        row.heroImageUrl || undefined,
        row.authors || undefined,
      );

      textFile.markAsExisting();
      return textFile;
    } else {
      // 创建基础 Snip 实体
      const snip = new Snip(
        row.id,
        row.createdAt,
        row.updatedAt,
        row.creatorId,
        row.spaceId,
        row.parentId,
        row.title || '',
        row.extra,
        row.type as SnipType,
        row.from as SnipFrom,
        row.status as SnipStatus,
        row.visibility as Visibility,
        boardItemInfo?.boardId,
        boardItemInfo,
        position,
      );

      snip.markAsExisting();
      return snip;
    }
  }

  /**
   * 保存 board_item 记录
   */
  private async saveBoardItem(
    entityType: 'snip' | 'thought' | 'board_group',
    entityId: string,
    position: BoardPositionInfo,
    trx?: any,
  ): Promise<string> {
    const db = trx || this.db;

    const boardItemDO = {
      boardId: position.boardId,
      rank: position.rank,
      parentBoardGroupId: position.parentBoardGroupId || null,
      snipId: entityType === 'snip' ? entityId : null,
      thoughtId: entityType === 'thought' ? entityId : null,
      boardGroupId: entityType === 'board_group' ? entityId : null,
      chatId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

    const result = await db.insert(boardItems).values(boardItemDO).returning({ id: boardItems.id });
    return result[0].id;
  }

  /**
   * 更新或插入 board_item 记录
   */
  private async upsertBoardItem(
    entityType: 'snip' | 'thought' | 'board_group',
    entityId: string,
    position: BoardPositionInfo,
    trx?: any,
  ): Promise<string | undefined> {
    const db = trx || this.db;

    const entityIdField =
      entityType === 'snip' ? 'snipId' : entityType === 'thought' ? 'thoughtId' : 'boardGroupId';

    // 检查是否已存在
    const existing = await db
      .select()
      .from(boardItems)
      .where(eq(boardItems[entityIdField], entityId))
      .limit(1);

    const boardItemData = {
      boardId: position.boardId,
      rank: position.rank,
      parentBoardGroupId: position.parentBoardGroupId || null,
      updatedAt: new Date(),
    };

    if (existing.length > 0) {
      // 更新现有记录
      await db.update(boardItems).set(boardItemData).where(eq(boardItems[entityIdField], entityId));
      return existing[0].id;
    } else {
      // 插入新记录
      const boardItemId = await this.saveBoardItem(entityType, entityId, position, trx);
      return boardItemId;
    }
  }
}
