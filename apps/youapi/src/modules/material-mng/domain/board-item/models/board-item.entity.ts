import { AggregateRoot } from '@nestjs/cqrs';
import { BoardItemCreatedEvent } from '../events/board-item-created.event';
import { BoardItemDeletedEvent } from '../events/board-item-deleted.event';
import { BoardItemMovedEvent } from '../events/board-item-moved.event';

export const BoardItemType = {
  SNIP: 'snip',
  THOUGHT: 'thought',
  BOARD_GROUP: 'board_group',
  /** @deprecated */
  CHAT: 'chat',
} as const;

export type BoardItemType = (typeof BoardItemType)[keyof typeof BoardItemType];

export class BoardItem extends AggregateRoot {
  public isNew: boolean = false;

  constructor(
    public readonly id: string,
    public readonly createdAt: Date,
    public updatedAt: Date,
    public readonly boardId: string,
    public parentBoardGroupId: string | null,
    public readonly entityType: BoardItemType,
    public readonly entityId: string,
    public rank: string,
  ) {
    super();
  }

  static create(
    id: string,
    boardId: string,
    entityType: BoardItemType,
    entityId: string,
    parentBoardGroupId?: string,
    rank = '~',
  ): BoardItem {
    const now = new Date();
    const boardItem = new BoardItem(
      id,
      now,
      now,
      boardId,
      parentBoardGroupId || null,
      entityType,
      entityId,
      rank,
    );

    boardItem.isNew = true;
    boardItem.apply(
      new BoardItemCreatedEvent(
        boardItem.id,
        boardItem.boardId,
        boardItem.entityType,
        boardItem.entityId,
        boardItem.parentBoardGroupId,
        boardItem.rank,
      ),
    );

    return boardItem;
  }

  /**
   * 移动 BoardItem 到指定的 BoardGroup
   */
  moveToBoardGroup(parentBoardGroupId: string, rank: string): void {
    const oldParentBoardGroupId = this.parentBoardGroupId;
    const oldRank = this.rank;

    this.parentBoardGroupId = parentBoardGroupId;
    this.rank = rank;
    this.updatedAt = new Date();

    this.apply(
      new BoardItemMovedEvent(
        this.id,
        this.boardId,
        this.entityType,
        this.entityId,
        oldParentBoardGroupId,
        parentBoardGroupId,
        oldRank,
        rank,
      ),
    );
  }

  /**
   * 移动 BoardItem 到 Board 根目录
   */
  moveToRoot(rank: string): void {
    const oldParentBoardGroupId = this.parentBoardGroupId;
    const oldRank = this.rank;

    this.parentBoardGroupId = null;
    this.rank = rank;
    this.updatedAt = new Date();

    this.apply(
      new BoardItemMovedEvent(
        this.id,
        this.boardId,
        this.entityType,
        this.entityId,
        oldParentBoardGroupId,
        null,
        oldRank,
        rank,
      ),
    );
  }

  /**
   * 删除 BoardItem
   */
  delete(): void {
    this.apply(new BoardItemDeletedEvent(this.id, this.boardId, this.entityType, this.entityId));
  }

  markAsExisting(): void {
    this.isNew = false;
  }

  /**
   * 检查是否在指定的 BoardGroup 中
   */
  isInBoardGroup(boardGroupId: string): boolean {
    return this.parentBoardGroupId === boardGroupId;
  }

  /**
   * 检查是否在根目录
   */
  isInRoot(): boolean {
    return this.parentBoardGroupId === null;
  }

  /**
   * 获取实体类型的友好名称
   */
  getEntityTypeName(): string {
    switch (this.entityType) {
      case BoardItemType.SNIP:
        return 'Snip';
      case BoardItemType.THOUGHT:
        return 'Thought';
      case BoardItemType.BOARD_GROUP:
        return 'Board Group';
      case BoardItemType.CHAT:
        return 'Chat';
      default:
        return 'Unknown';
    }
  }
}
