import { forward<PERSON><PERSON>, Module } from '@nestjs/common';
import { DomainModule } from '@/domain/domain.module';
import { InfraModule } from '@/infra/infra.module';
import { AiModule } from '../ai/ai.module';
import { IamModule } from '../iam/iam.module';
import { BoardController } from './controllers/board.controller';
import { BoardGroupController } from './controllers/board-group.controller';
import { BoardItemController } from './controllers/board-item.controller';
import { DiffController } from './controllers/diff.controller';
import { EntityController } from './controllers/entity.controller';
import { FavoriteController } from './controllers/favorite.controller';
import { FileController } from './controllers/file.controller';
import { MaterialController } from './controllers/material.controller';
import { NoteController } from './controllers/note.controller';
import { PlaylistItemController } from './controllers/playlist-item.controller';
import { PositionController } from './controllers/position.controller';
import { Share<PERSON>ontroller } from './controllers/share.controller';
import { ShortLinkController } from './controllers/short-link.controller';
import { SnipController } from './controllers/snip.controller';
import { TextController } from './controllers/text.controller';
import { ThoughtController } from './controllers/thought.controller';
import { ThoughtVersionController } from './controllers/thought-version.controller';
import { UsageRecordController } from './controllers/usage-record.controller';
import { WebhookController } from './controllers/webhook.controller';
import { BoardPositionDomainService } from './domain/shared/board-position.service';
import { BlockRepository } from './repositories/block.repository';
import { BlockContentRepository } from './repositories/block-content.repository';
import { BoardRepository } from './repositories/board.repository';
import { BoardGroupRepository } from './repositories/board-group.repository';
import { BoardItemRepository } from './repositories/board-item.repository';
import { DiffReviewEventRepository } from './repositories/diff-review-event.repository';
import { FavoriteRepository } from './repositories/favorite.repository';
import { NoteRepository } from './repositories/note.repository';
import { PlaylistItemRepository } from './repositories/playlist-item.repository';
import { ShortLinkRepository } from './repositories/short-link.repository';
import { SnipRepository } from './repositories/snip.repository';
import { ThoughtRepository } from './repositories/thought.repository';
import { ThoughtVersionRepository } from './repositories/thought-version.repository';
import { BlockDtoService } from './services/dto-services/block-dto.service';
import { BoardDtoService } from './services/dto-services/board-dto.service';
import { BoardGroupDtoService } from './services/dto-services/board-group-dto.service';
import { FavoriteDtoService } from './services/dto-services/favorite-dto.service';
import { NoteDtoService } from './services/dto-services/note-dto.service';
import { PlaylistItemDtoService } from './services/dto-services/playlist-item-dto.service';
import { ShortLinkDtoService } from './services/dto-services/short-link-dto.service';
import { SnipDtoService } from './services/dto-services/snip-dto.service';
import { ThoughtDtoService } from './services/dto-services/thought-dto.service';
import { ThoughtVersionDtoService } from './services/dto-services/thought-version-dto.service';
import { ArchiveBoardHandler } from './services/handlers/board/archive-board.handler';
import { CreateBoardHandler } from './services/handlers/board/create-board.handler';
import { DeleteBoardHandler } from './services/handlers/board/delete-board.handler';
import { GetBoardHandler } from './services/handlers/board/get-board.handler';
import { GetBoardDetailHandler } from './services/handlers/board/get-board-detail.handler';
import { GetBoardDirectoryStructureHandler } from './services/handlers/board/get-board-directory-structure.handler';
import { ListBoardsHandler } from './services/handlers/board/list-boards.handler';
import { ListBoardsWithSomeBoardItemsHandler } from './services/handlers/board/list-boards-with-some-board-items.handler';
import { ListSimpleBoardsByEntityIdsHandler } from './services/handlers/board/list-simple-boards-by-entity-ids.handler';
import { PinBoardHandler } from './services/handlers/board/pin-board.handler';
import { UnarchiveBoardHandler } from './services/handlers/board/unarchive-board.handler';
import { UnpinBoardHandler } from './services/handlers/board/unpin-board.handler';
import { UpdateBoardHandler } from './services/handlers/board/update-board.handler';
import { UserInitializedHandler } from './services/handlers/board/user-initialized.handler';
import { CreateBoardGroupHandler } from './services/handlers/board-group/create-board-group.handler';
import { DeleteBoardGroupHandler } from './services/handlers/board-group/delete-board-group.handler';
import { UngroupBoardGroupHandler } from './services/handlers/board-group/ungroup-board-group.handler';
import { UpdateBoardGroupHandler } from './services/handlers/board-group/update-board-group.handler';
import { ReportDiffReviewEventHandler } from './services/handlers/diff/report-diff-review-event.handler';
import { GetEntitiesHandler } from './services/handlers/entity/get-entities.handler';
import { ListEntitiesForAtReferenceHandler } from './services/handlers/entity/list-entities-for-at-reference.handler';
import { FavoriteEntityHandler } from './services/handlers/favorite/favorite-entity.handler';
import { ListFavoritesHandler } from './services/handlers/favorite/list-favorites.handler';
import { MoveFavoriteHandler } from './services/handlers/favorite/move-favorite.handler';
import { UnfavoriteEntityHandler } from './services/handlers/favorite/unfavorite-entity.handler';
import { GenSignedPutUrlIfNotExistHandler } from './services/handlers/file/gen-signed-put-url-if-not-exist.handler';
import { UploadFileHandler } from './services/handlers/file/upload-file.handler';
import { UploadSvgHandler } from './services/handlers/file/upload-svg.handler';
import { ListMaterialsHandler } from './services/handlers/material/list-materials.handler';
import { ListRecentMaterialsHandler } from './services/handlers/material/list-recent-materials.handler';
import { ListUnusedMaterialsHandler } from './services/handlers/material/list-unused-materials.handler';
import { ListUnusedMaterialsForMobileHandler } from './services/handlers/material/list-unused-materials-for-mobile.handler';
import { PublishMaterialHandler } from './services/handlers/material/publish-material.handler';
import { SaveSharedMaterialHandler } from './services/handlers/material/save-shared-material.handler';
import { UnpublishMaterialHandler } from './services/handlers/material/unpublish-material.handler';
import { MoveBoardItemToBoardGroupHandler } from './services/handlers/move/move-board-item-to-board-group.handler';
import { MoveBoardItemToRootHandler } from './services/handlers/move/move-board-item-to-root.handler';
import { MoveEntityToUnsortedHandler } from './services/handlers/move/move-entity-to-unsorted.handler';
import { MoveItemHandler } from './services/handlers/move/move-item.handler';
import { CreateNoteHandler } from './services/handlers/note/create-note.handler';
import {
  DeleteManyNotesHandler,
  DeleteNoteHandler,
} from './services/handlers/note/delete-note.handler';
import { ListNotesHandler } from './services/handlers/note/list-notes.handler';
import { UpdateNoteHandler } from './services/handlers/note/update-note.handler';
// Playlist Item Handlers
import { CreatePlaylistItemHandler } from './services/handlers/playlist-item/create-playlist-item.handler';
import { DeletePlaylistItemHandler } from './services/handlers/playlist-item/delete-playlist-item.handler';
import { ListPlaylistItemsHandler } from './services/handlers/playlist-item/list-playlist-items.handler';
import { UpdatePlaylistItemStatusHandler } from './services/handlers/playlist-item/update-playlist-item-status.handler';
import { UpdatePlaylistItemTitleHandler } from './services/handlers/playlist-item/update-playlist-item-title.handler';
import { GetSharedEntityHandler } from './services/handlers/share/get-shared-entity.handler';
import { ActivateShortLinkHandler } from './services/handlers/short-link/activate-short-link.handler';
import { CreateOrGetShortLinkHandler } from './services/handlers/short-link/create-or-get-short-link.handler';
import { DeactivateShortLinkHandler } from './services/handlers/short-link/deactivate-short-link.handler';
import { GetShortLinkByEntityHandler } from './services/handlers/short-link/get-short-link-by-entity.handler';
import { GetShortLinkByShortIdHandler } from './services/handlers/short-link/get-short-link-by-short-id.handler';
import { AddPunctuationHandler } from './services/handlers/snip/add-punctuation.handler';
import { CallOverviewHandler } from './services/handlers/snip/call-overview.handler';
import { CloneSnipHandler } from './services/handlers/snip/clone-snip.handler';
import { CreateArticleHandler } from './services/handlers/snip/create-article.handler';
import { CreateImageHandler } from './services/handlers/snip/create-image.handler';
import { CreateOfficeHandler } from './services/handlers/snip/create-office.handler';
import { CreateOtherWebpageHandler } from './services/handlers/snip/create-other-webpage.handler';
import { CreatePdfHandler } from './services/handlers/snip/create-pdf.handler';
import { CreatePdfByUrlHandler } from './services/handlers/snip/create-pdf-by-url.handler';
import { CreateSnipBlocksHandler } from './services/handlers/snip/create-snip-blocks.handler';
import { CreateSnippetHandler } from './services/handlers/snip/create-snippet.handler';
import { CreateTextFileHandler } from './services/handlers/snip/create-text-file.handler';
import { CreateUnknownWebpageHandler } from './services/handlers/snip/create-unknown-webpage.handler';
import { CreateVideoHandler } from './services/handlers/snip/create-video.handler';
import { CreateVoiceHandler } from './services/handlers/snip/create-voice.handler';
import { CreateVoiceByUrlHandler } from './services/handlers/snip/create-voice-by-url.handler';
import { DeleteFormattedSubtitlesHandler } from './services/handlers/snip/delete-formatted-subtitles.handler';
import { DeleteSnipHandler } from './services/handlers/snip/delete-snip.handler';
import { DetectSpeakersHandler } from './services/handlers/snip/detect-speakers.handler';
import { EditImageHandler } from './services/handlers/snip/edit-image.handler';
import { ExtractContentHandler } from './services/handlers/snip/extract-content.handler';
import { ExtractTranscriptHandler } from './services/handlers/snip/extract-transcript.handler';
import { GenerateImageInfoHandler } from './services/handlers/snip/generate-image-info.handler';
import { GenerateOverviewHandler } from './services/handlers/snip/generate-overview.handler';
import { GenerateTranscriptHandler } from './services/handlers/snip/generate-transcript.handler';
// TODO: 其他 Snip handlers 将在依赖修复后添加
import { GetSnipHandler } from './services/handlers/snip/get-snip.handler';
import { GetSnipsHandler } from './services/handlers/snip/get-snips.handler';
import { GetUserIdFromSnipHandler } from './services/handlers/snip/get-user-id-from-snip.handler';
import { GetWebpageByNormalizedUrlHandler } from './services/handlers/snip/get-webpage-by-normalized-url.handler';
import { ListFormattedSubtitlesHandler } from './services/handlers/snip/list-formatted-subtitles.handler';
import { ListSnipsHandler } from './services/handlers/snip/list-snips.handler';
import { ListSnipsByUrlHandler } from './services/handlers/snip/list-snips-by-url.handler';
import { ListWebpagesByUrlsHandler } from './services/handlers/snip/list-webpages-by-urls.handler';
import { ListWebpagesInBoardByUrlsHandler } from './services/handlers/snip/list-webpages-in-board-by-urls.handler';
import { PatchVideoTranscriptHandler } from './services/handlers/snip/patch-video-transcript.handler';
import { PatchVoiceTranscriptHandler } from './services/handlers/snip/patch-voice-transcript.handler';
import { PublishSnipHandler } from './services/handlers/snip/publish-snip.handler';
import { TryCreateSnipByUrlHandler } from './services/handlers/snip/try-create-snip-by-url.handler';
import { UnpublishSnipHandler } from './services/handlers/snip/unpublish-snip.handler';
import { UpdateImageHandler } from './services/handlers/snip/update-image.handler';
import { UpdateSnipPlayUrlHandler } from './services/handlers/snip/update-snip-play-url.handler';
import { UpdateSnipTitleHandler } from './services/handlers/snip/update-snip-title.handler';
import { UpdateTranscriptSpeakerHandler } from './services/handlers/snip/update-transcript-speaker.handler';
import { ExtractTextHandler } from './services/handlers/text/extract-text.handler';
import { CloneThoughtHandler } from './services/handlers/thought/clone-thought.handler';
import { CreateThoughtHandler } from './services/handlers/thought/create-thought.handler';
import { DeleteThoughtHandler } from './services/handlers/thought/delete-thought.handler';
import { GenThoughtTitleHandler } from './services/handlers/thought/gen-thought-title.handler';
import { GetThoughtHandler } from './services/handlers/thought/get-thought.handler';
import { ListThoughtsHandler } from './services/handlers/thought/list-thoughts.handler';
import { PublishThoughtHandler } from './services/handlers/thought/publish-thought.handler';
import { UnpublishThoughtHandler } from './services/handlers/thought/unpublish-thought.handler';
import { UpdateThoughtHandler } from './services/handlers/thought/update-thought.handler';
import { CreateThoughtVersionHandler } from './services/handlers/thought-version/create-thought-version.handler';
import { DeleteThoughtVersionHandler } from './services/handlers/thought-version/delete-thought-version.handler';
import { GetThoughtVersionHandler } from './services/handlers/thought-version/get-thought-version.handler';
import { ListThoughtVersionsHandler } from './services/handlers/thought-version/list-thought-versions.handler';
import { CreateStorageUsageRecordFromEditorHandler } from './services/handlers/usage-record/create-storage-usage-record-from-editor.handler';
import { WebhookFetchedHandler } from './services/handlers/webhook/webhook-fetched.handler';
import { WebhookImagesTransferedHandler } from './services/handlers/webhook/webhook-images-transfered.handler';
import { WebhookOfficeParsedHandler } from './services/handlers/webhook/webhook-office-parsed.handler';
import { WebhookPdfParsedHandler } from './services/handlers/webhook/webhook-pdf-parsed.handler';
import { WebhookSubtitleTranscribedHandler } from './services/handlers/webhook/webhook-subtitle-transcribed.handler';
// import { BoardItemRepository } from './repositories/board-item.repository'; // 已移除，使用统一的位置管理
import { TempSnipService } from './services/temp-domain.service';
@Module({
  imports: [
    DomainModule, // 导入领域模块（包含 ChatDomainService 服务）
    InfraModule, // 导入基础设施模块（包含 Youget 服务）
    forwardRef(() => IamModule), // 导入 IAM 模块（包含 UserPreferenceRepository）
    forwardRef(() => AiModule), // 导入 AI 模块（包含 OverviewService）
  ],
  controllers: [
    NoteController,
    ThoughtController,
    ThoughtVersionController,
    UsageRecordController,
    DiffController,
    BoardGroupController,
    BoardItemController,
    PositionController,
    BoardController,
    SnipController,
    ShareController,
    ShortLinkController,
    FavoriteController,
    MaterialController,
    PlaylistItemController,
    EntityController,
    WebhookController,
    FileController,
    TextController,
  ],
  providers: [
    // 仓储层
    BlockRepository,
    BlockContentRepository,
    NoteRepository,
    ThoughtRepository,
    ThoughtVersionRepository,
    DiffReviewEventRepository,
    BoardRepository,
    BoardGroupRepository,
    BoardItemRepository,
    SnipRepository,
    ShortLinkRepository,
    FavoriteRepository,
    PlaylistItemRepository,

    // dto 服务
    BlockDtoService,
    NoteDtoService,
    BoardGroupDtoService,
    ThoughtDtoService,
    BoardDtoService,
    ThoughtVersionDtoService,
    SnipDtoService,
    FavoriteDtoService,
    ShortLinkDtoService,
    PlaylistItemDtoService,

    // 临时领域服务（避免导入 domain 目录）
    TempSnipService,

    // 新增：统一位置管理服务
    BoardPositionDomainService,

    // 新增：内容提取处理器
    ExtractContentHandler,

    // CQRS 处理器 - Note
    CreateNoteHandler,
    UpdateNoteHandler,
    DeleteNoteHandler,
    DeleteManyNotesHandler,
    ListNotesHandler,

    // CQRS 处理器 - Thought
    CreateThoughtHandler,
    UpdateThoughtHandler,
    DeleteThoughtHandler,
    GenThoughtTitleHandler,
    PublishThoughtHandler,
    UnpublishThoughtHandler,
    GetThoughtHandler,
    ListThoughtsHandler,
    CloneThoughtHandler,

    // CQRS 处理器 - Snip
    AddPunctuationHandler,
    CreateArticleHandler,
    CreateImageHandler,
    EditImageHandler,
    CreateOfficeHandler,
    CreateOtherWebpageHandler,
    CreatePdfHandler,
    CreatePdfByUrlHandler,
    CreateSnipBlocksHandler,
    CreateSnippetHandler,
    CreateTextFileHandler,
    CreateUnknownWebpageHandler,
    CreateVideoHandler,
    CreateVoiceHandler,
    CreateVoiceByUrlHandler,
    DeleteSnipHandler,
    DeleteFormattedSubtitlesHandler,
    ListFormattedSubtitlesHandler,
    DetectSpeakersHandler,
    GenerateImageInfoHandler,
    GenerateOverviewHandler,
    GenerateTranscriptHandler,
    TryCreateSnipByUrlHandler,
    UpdateImageHandler,
    UpdateSnipTitleHandler,
    UpdateSnipPlayUrlHandler,
    UpdateTranscriptSpeakerHandler,
    PatchVoiceTranscriptHandler,
    PatchVideoTranscriptHandler,
    CallOverviewHandler,
    ExtractTranscriptHandler,
    CloneSnipHandler,
    ListSnipsHandler,
    ListSnipsByUrlHandler,
    ListWebpagesByUrlsHandler,
    ListWebpagesInBoardByUrlsHandler,
    GetWebpageByNormalizedUrlHandler,
    GenerateOverviewHandler,
    // TODO: 其他 Snip handlers 将在依赖修复后添加
    GetSnipHandler,
    GetSnipsHandler,
    GetUserIdFromSnipHandler,

    // CQRS 处理器 - Thought Version
    ListThoughtVersionsHandler,
    GetThoughtVersionHandler,
    CreateThoughtVersionHandler,
    CreateThoughtVersionHandler,
    DeleteThoughtVersionHandler,
    ReportDiffReviewEventHandler,

    // CQRS 处理器 - Board
    CreateBoardHandler,
    UpdateBoardHandler,
    DeleteBoardHandler,
    GetBoardHandler,
    GetBoardDetailHandler,
    GetBoardDirectoryStructureHandler,
    ListBoardsHandler,
    ListBoardsWithSomeBoardItemsHandler,
    ListSimpleBoardsByEntityIdsHandler,
    PinBoardHandler,
    UnpinBoardHandler,
    ArchiveBoardHandler,
    UnarchiveBoardHandler,
    UserInitializedHandler, // 事件处理器

    // CQRS 处理器 - Board Group
    CreateBoardGroupHandler,
    UpdateBoardGroupHandler,
    DeleteBoardGroupHandler,
    UngroupBoardGroupHandler,

    // CQRS 处理器 - Board Item
    MoveBoardItemToBoardGroupHandler,
    MoveBoardItemToRootHandler,

    // CQRS 处理器 - Move
    MoveEntityToUnsortedHandler,

    // 新增：统一移动处理器
    MoveItemHandler,

    // CQRS 处理器 - Favorite
    ListFavoritesHandler,
    FavoriteEntityHandler,
    UnfavoriteEntityHandler,
    MoveFavoriteHandler,

    // CQRS 处理器 - Share
    GetSharedEntityHandler,

    // CQRS 处理器 - Short Link
    CreateOrGetShortLinkHandler,
    ActivateShortLinkHandler,
    DeactivateShortLinkHandler,
    GetShortLinkByEntityHandler,
    GetShortLinkByShortIdHandler,

    // CQRS 处理器 - Material
    ListMaterialsHandler,
    ListRecentMaterialsHandler,
    ListUnusedMaterialsHandler,
    ListUnusedMaterialsForMobileHandler,
    PublishMaterialHandler,
    UnpublishMaterialHandler,
    SaveSharedMaterialHandler,
    PublishSnipHandler,
    UnpublishSnipHandler,

    // CQRS 处理器 - Playlist Item
    CreatePlaylistItemHandler,
    DeletePlaylistItemHandler,
    ListPlaylistItemsHandler,
    UpdatePlaylistItemStatusHandler,
    UpdatePlaylistItemTitleHandler,

    // CQRS 处理器 - Entity
    GetEntitiesHandler,
    ListEntitiesForAtReferenceHandler,

    // CQRS 处理器 - Webhook
    WebhookImagesTransferedHandler,
    WebhookOfficeParsedHandler,
    WebhookPdfParsedHandler,
    WebhookSubtitleTranscribedHandler,
    WebhookFetchedHandler,

    // CQRS 处理器 - File
    UploadFileHandler,
    UploadSvgHandler,
    GenSignedPutUrlIfNotExistHandler,
    CreateStorageUsageRecordFromEditorHandler,

    // CQRS 处理器 - Text
    ExtractTextHandler,
  ],
  exports: [BoardRepository],
})
export class MaterialMngModule {}
