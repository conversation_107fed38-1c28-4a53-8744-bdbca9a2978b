# Service 层优化总结

本文档记录了基于优化后的 Repository 层，对 Service 层进行的批量查询优化，完全参考 youapp 的 `boards2withSomeBoardItemsVOs` 逻辑。

## 优化概览

### 原始实现问题
- **N+1 查询**: 对每个看板单独查询 items 和 count
- **实体查询 N+1**: 对每个 boardItem 单独查询对应的实体
- **性能极差**: 10个看板，每个10个items，总查询次数约 120+ 次

### 优化后实现
- **批量查询**: 只需 `2N + 2` 次查询（N个看板的items/count查询 + 2次批量实体查询）
- **内存映射**: 使用 Map 进行高效实体匹配  
- **内容裁剪**: Snip 仅对 SNIPPET 类型返回完整内容

## 详细改动

### 1. BoardDtoService 重构

**文件**: `src/modules/material-mng/services/dto-services/board-dto.service.ts`

#### 1.1 添加依赖注入
```typescript
constructor(
  private readonly boardRepository: BoardRepository,
  private readonly boardItemRepository: BoardItemRepository, // 新增
  private readonly snipRepository: SnipRepository,
  private readonly thoughtRepository: ThoughtRepository,
  private readonly snipDtoService: SnipDtoService,
  private readonly thoughtDtoService: ThoughtDtoService,
) {}
```

#### 1.2 重写核心方法

**完全重写 `toBoardWithSomeBoardItemsDtoList`**:

```typescript
/**
 * 优化版本：批量获取看板及其项目，参考 youapp 的 boards2withSomeBoardItemsVOs 逻辑
 */
async toBoardWithSomeBoardItemsDtoList(
  boards: Board[],
  limit: number = 10,
): Promise<BoardWithSomeBoardItemsDto[]>
```

**实现步骤**:

1. **并行获取看板数据**:
   ```typescript
   const boardsWithData = await Promise.all(
     boards.map(async (board) => {
       const [{ data: items }, itemsCount] = await Promise.all([
         this.boardItemRepository.findByCursorExcludeGroup(board.id, limit),
         this.boardItemRepository.countExcludeGroup(board.id),
       ]);
       return { board, items, itemsCount };
     }),
   );
   ```

2. **收集所有实体ID**:
   ```typescript
   const allSnipIds = new Set<string>();
   const allThoughtIds = new Set<string>();
   const allChatIds = new Set<string>();
   
   boardsWithData.forEach(({ items }) => {
     items.forEach((item) => {
       if (item.entityType === 'snip' && item.entityId) {
         allSnipIds.add(item.entityId);
       }
       // ... 其他类型
     });
   });
   ```

3. **批量查询所有实体**:
   ```typescript
   const [snips, thoughts] = await Promise.all([
     allSnipIds.size > 0
       ? this.snipRepository.findByIdsWithContentTruncated({
           ids: Array.from(allSnipIds),
           includeDeleted: false,
         })
       : [],
     allThoughtIds.size > 0 
       ? this.thoughtRepository.findByIds(Array.from(allThoughtIds)) 
       : [],
   ]);
   ```

4. **创建查找映射**:
   ```typescript
   const snipMap = new Map(snips.map((s) => [s.id, s]));
   const thoughtMap = new Map(thoughts.map((t) => [t.id, t]));
   ```

5. **组装最终结果**:
   ```typescript
   return boardsWithData.map(({ board, items, itemsCount }) => ({
     ...this.toBoardDto(board),
     itemsCount,
     items: items.map((item) => this.createBoardItemDto(item, snipMap, thoughtMap)),
   }));
   ```

#### 1.3 新增辅助方法

**`createBoardItemDto` 方法**:
- 从内存映射中获取实体，避免数据库查询
- 处理不同类型的实体（snip, thought, chat）
- 创建标准的 BoardItemDto 结构

#### 1.4 删除废弃方法
- 删除 `toBoardWithSomeBoardItemsDto` (单个看板处理)
- 删除 `populateBoardItemEntities` (N+1 查询方法)

## 性能提升对比

### 查询次数对比

假设场景：10个看板，每个看板10个items

#### 优化前 (youapi 原实现)
```
基础查询: 1次 (获取看板列表)
每个看板:
  - 获取items: 1次
  - 获取count: 1次  
  - 每个item的实体查询: 10次
  
总计: 1 + 10 × (2 + 10) = 121次查询
```

#### 优化后 (参考youapp)
```
基础查询: 1次 (获取看板列表)
每个看板:
  - 获取items: 1次
  - 获取count: 1次
批量实体查询:
  - snips: 1次 (内容裁剪)
  - thoughts: 1次
  
总计: 1 + 10 × 2 + 2 = 23次查询
```

**性能提升: 81% 查询次数减少**

### 数据传输优化

1. **Snip 内容裁剪**:
   - 仅 SNIPPET 类型返回完整 `contentRaw` 和 `contentPlain`
   - 其他类型（Article、PDF、Image等）返回空字符串
   - 数据传输量显著减少

2. **内存映射查找**:
   - O(1) 时间复杂度的实体查找
   - 避免重复的数据库访问

## 关键优化点

### 1. 批量查询策略
- **并行查询**: 看板数据并行获取
- **ID收集**: 一次遍历收集所有实体ID
- **批量实体查询**: 最少的数据库往返

### 2. 内容裁剪优化
- **SQL层裁剪**: 在数据库层面进行内容过滤
- **类型条件**: 基于实体类型智能裁剪
- **传输效率**: 减少网络传输数据量

### 3. 内存优化
- **Set去重**: 避免重复ID查询
- **Map查找**: O(1)时间复杂度的实体匹配
- **惰性加载**: 只查询实际需要的实体类型

## 代码质量特点

### 1. 可读性
- **清晰的步骤注释**: 每个处理步骤都有明确说明
- **语义化方法名**: `createBoardItemDto`, `findByCursorExcludeGroup`
- **类型安全**: 完整的 TypeScript 类型定义

### 2. 可维护性
- **单一职责**: 每个方法职责明确
- **模块化设计**: Repository、Service、DTO 分层清晰
- **易于测试**: 逻辑分离，便于单元测试

### 3. 扩展性
- **支持新实体类型**: 通过扩展映射逻辑支持更多实体
- **灵活的查询参数**: 支持不同的limit和过滤条件
- **游标分页支持**: 为未来的分页需求做准备

## 预期效果

1. **响应时间提升**: 5-10倍性能提升
2. **数据库负载降低**: 80%+ 查询次数减少
3. **网络传输优化**: 50%+ 数据传输量减少
4. **前端渲染改善**: 更快的数据加载和页面渲染
5. **用户体验提升**: 看板列表加载流畅性显著改善

## 下一步工作

1. **测试验证**: 编写集成测试验证优化效果
2. **监控添加**: 添加性能监控指标
3. **A/B测试**: 对比优化前后的实际性能数据
4. **扩展应用**: 将类似优化应用到其他列表类接口