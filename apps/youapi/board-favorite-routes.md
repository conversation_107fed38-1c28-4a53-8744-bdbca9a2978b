# Board & Favorite 接口梳理文档

## 概览

基于 `youapp-routes.md` 梳理的 Board（创作板）和 Favorite（收藏）相关接口，包含接口路径、迁移状态和功能说明。

**统计信息：**
- **Board 相关**: 19 个接口
- **Favorite 相关**: 4 个接口
- **总计**: 23 个接口
- **已迁移**: 20 个接口 (87.0%)
- **未迁移**: 3 个接口 (13.0%)

---

## 🗂️ Board 模块接口

### 1. Board 核心模块 (12个接口)

| 接口路径 | 功能描述 | 迁移状态 | 备注 |
|---------|----------|----------|------|
| `/api/v1/board/archiveBoard` | 归档创作板 | ⚠️ 未测试 | |
| `/api/v1/board/getBoard` | 获取创作板信息 | ⚠️ 未测试 | |
| `/api/v1/board/getBoardDetail` | 获取创作板详情（含内容项） | ⚠️ 未测试 |
| `/api/v1/board/getDefaultBoard` | 获取默认创作板 | ⚠️ 未测试 | |
| `/api/v1/board/listBoards` | 列出创作板列表 | ⚠️ 未测试 | |
| `/api/v1/board/listBoardsWithSomeBoardItems` | 列出创作板及部分内容项 | ⚠️ 已测试 | voice 相关问题已修复 |
| `/api/v1/board/moveBoard` | 移动创作板位置 | ❌ 未迁移 | 待确定是否需要 |
| `/api/v1/board/patchBoard` | 更新创作板信息 | ⚠️ 未测试 | |
| `/api/v1/board/pinBoard` | 置顶创作板 | ⚠️ 未测试 | |
| `/api/v1/board/unarchiveBoard` | 取消归档创作板 | ⚠️ 未测试 | |
| `/api/v1/board/unpinBoard` | 取消置顶创作板 | ⚠️ 未测试 | |
| `/api/v1/createBoard` | 创建创作板 | ⚠️ 未测试 | |

**迁移进度**: 11/12 (91.7%)

### 2. Board-group 模块 (4个接口)

| 接口路径 | 功能描述 | 迁移状态 | 备注 |
|---------|----------|----------|------|
| `/api/v1/board-group/createBoardGroup` | 创建创作板分组 | ⚠️ 未测试 | |
| `/api/v1/board-group/deleteBoardGroup` | 删除创作板分组 | ⚠️ 未测试 | |
| `/api/v1/board-group/patchBoardGroup` | 更新创作板分组信息 | ⚠️ 未测试 | |
| `/api/v1/board-group/ungroup` | 取消分组 | ⚠️ 未测试 | |

**迁移进度**: 4/4 (100%)

### 3. BoardItem 模块 (3个接口)

| 接口路径 | 功能描述 | 迁移状态 | 备注 |
|---------|----------|----------|------|
| `/api/v1/boardItem/duplicate` | 复制创作板项目 | ❌ 未迁移 | 待确定是否需要 |
| `/api/v1/boardItem/moveBoardItemToBoardGroup` | 将项目移动到分组 | ⚠️ 未测试 | |
| `/api/v1/boardItem/moveBoardItemToRoot` | 将项目移动到根目录 | ⚠️ 未测试 | |

**迁移进度**: 2/3 (66.7%)

---

## ⭐ Favorite 模块接口 (4个接口)

| 接口路径 | 功能描述 | 迁移状态 | 备注 |
|---------|----------|----------|------|
| `/api/v1/favorite/favoriteEntity` | 收藏实体（snip/thought/board等） | ⚠️ 未测试 | |
| `/api/v1/favorite/listFavorites` | 列出收藏列表 | ⚠️ 未测试 | |
| `/api/v1/favorite/moveFavorite` | 移动收藏项位置 | ⚠️ 未测试 | |
| `/api/v1/favorite/unfavoriteEntity` | 取消收藏实体 | ⚠️ 未测试 | |

**迁移进度**: 4/4 (100%)

---

## 🔗 相关接口

### 根目录中的 Board 相关接口

| 接口路径 | 功能描述 | 迁移状态 | 备注 |
|---------|----------|----------|------|
| `/api/v1/createBoardFromTemplate` | 从模板创建创作板 | ⚠️ 未测试 | |
| `/api/v1/deleteBoard` | 删除创作板 | ⚠️ 未测试 | |
| `/api/v1/listBoards` | 列出创作板（根路径版本） | ⚠️ 未测试 | |
| `/api/v1/listSimpleBoardsByEntityIds` | 根据实体ID列出简化创作板信息 | ⚠️ 未测试 | 未测试 |
| `/api/v1/moveEntityToBoard` | 将实体移动到创作板 | ⚠️ 未测试 | |

### V2 API

| 接口路径 | 功能描述 | 迁移状态 | 备注 |
|---------|----------|----------|------|
| `/api/v2/agent/newBoard/run` | Agent 创建新创作板 | ❌ 未标记 | V2 API，状态未标记 |

### 已废弃接口

| 接口路径 | 功能描述 | 迁移状态 | 备注 |
|---------|----------|----------|------|
| `/api/v1/putSnipBoards` | 设置片段所属创作板 | ⚪ 无需迁移 | 已废弃功能 |

---

## 📊 迁移状态统计

### 按模块统计

| 模块 | 总接口数 | 已迁移 | 未迁移 | 迁移率 |
|------|----------|--------|--------|--------|
| **Board 核心** | 12 | 11 | 1 | 91.7% |
| **Board-group** | 4 | 4 | 0 | 100% |
| **BoardItem** | 3 | 2 | 1 | 66.7% |
| **Favorite** | 4 | 4 | 0 | 100% |
| **根目录 Board 相关** | 6 | 5 | 1 | 83.3% |
| **V2 API** | 1 | 0 | 1 | 0% |

### 总体统计

- **总接口数**: 23 个
- **已完成迁移**: 17 个 (73.9%)
- **已迁移待测试**: 3 个 (13.0%)
- **未迁移**: 3 个 (13.0%)
- **实际迁移率**: 20/23 (87.0%)

---

## 🚀 功能特性

### Board 模块核心功能

1. **生命周期管理**
   - 创建：`createBoard`, `createBoardFromTemplate`
   - 查询：`getBoard`, `getBoardDetail`, `listBoards`
   - 更新：`patchBoard`
   - 删除：`deleteBoard`

2. **状态管理**
   - 归档/取消归档：`archiveBoard`, `unarchiveBoard`
   - 置顶/取消置顶：`pinBoard`, `unpinBoard`
   - 移动：`moveBoard`

3. **分组管理**
   - 创建分组：`createBoardGroup`
   - 更新分组：`patchBoardGroup`
   - 删除分组：`deleteBoardGroup`
   - 取消分组：`ungroup`

4. **内容管理**
   - 项目移动：`moveBoardItemToBoardGroup`, `moveBoardItemToRoot`
   - 项目复制：`duplicate` (未迁移)
   - 实体关联：`moveEntityToBoard`

### Favorite 模块核心功能

1. **收藏管理**
   - 添加收藏：`favoriteEntity`
   - 取消收藏：`unfavoriteEntity`
   - 收藏列表：`listFavorites`
   - 收藏排序：`moveFavorite`

2. **支持实体类型**
   - Snip（片段）
   - Thought（想法）
   - Board（创作板）
   - 其他实体类型

---

## ⚠️ 注意事项

### 需要重点关注的接口

1. **`getBoardDetail`** - 涉及多种 snip 类型，需要更全面的测试
2. **`listBoardsWithSomeBoardItems`** - voice 相关问题已修复，但仍需测试
3. **`createBoardNotMagic`** - 仅迁移了一半，大模型相关功能未完成

### 未迁移接口的影响评估

1. **`board/moveBoard`** - 创作板位置调整功能，待确定业务需求
2. **`boardItem/duplicate`** - 项目复制功能，待确定业务需求
3. **`v2/agent/newBoard/run`** - V2 API Agent 功能，属于新功能范畴

### 架构特点

- 使用 DDD + CQRS 架构模式
- 支持聚合根管理和事件驱动
- 完整的 Repository 模式实现
- 统一的错误处理和响应格式

---

**文档版本**: 1.0
**生成时间**: 2025-08-05
**数据来源**: youapp-routes.md
**维护状态**: 活跃维护
