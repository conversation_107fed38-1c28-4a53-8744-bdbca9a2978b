# Repository 层优化改动总结

本文档记录了为优化 `listBoardsWithSomeBoardItems` 接口性能，在 Repository 层所做的改动。这些改动模仿了 youapp 的实现方式，同时符合 youapi 的代码规范。

## 重要说明：内容裁剪策略

基于 youapp 的 SnipDAO `select(param: SelectSnipDOsParam)` 方法的裁剪规则：

### Snip 内容裁剪
- **仅对 SNIPPET 类型**: 返回完整的 `contentRaw` 和 `contentPlain`
- **其他类型** (Article、Image、Video、PDF、Office、TextFile 等): content 字段返回空字符串
- **注解字段**: `annotationRaw` 和 `annotationPlain` 保持原样，不进行裁剪

### Thought 内容处理
- **无需裁剪**: 返回完整的内容字段
- 方法名不体现内容相关特性

## 改动概览

### 1. SnipRepository 新增内容裁剪的批量查询方法

**文件**: `src/modules/material-mng/repositories/snip.repository.ts`

**新增方法**:
```typescript
/**
 * 批量查询 Snip - 内容有裁剪，仅 SNIPPET 类型返回完整内容
 * 其他类型（Article、Image、Video、PDF等）的 content 字段为空，以优化性能
 */
async findByIdsWithContentTruncated(params: BatchQueryParams): Promise<Snip[]>
```

**特点**:
- 使用 SQL CASE 语句实现类型条件裁剪
- 仅 SNIPPET 类型返回完整 `contentRaw` 和 `contentPlain`
- 其他类型返回空字符串，减少数据传输量
- 方法名明确标识内容被裁剪

**SQL 裁剪逻辑**:
```sql
contentRaw: CASE snips.type
  WHEN 'snippet' THEN snips.contentRaw
  ELSE ''
END
```

### 2. ThoughtRepository 新增批量查询方法

**文件**: `src/modules/material-mng/repositories/thought.repository.ts`

**新增方法**:
```typescript
/**
 * 批量查询想法 - 用于优化性能
 */
async findByIds(ids: string[]): Promise<Thought[]>
```

**特点**:
- 支持批量 ID 查询，避免 N+1 问题
- 返回完整内容，无裁剪处理
- 使用 `leftJoin` 确保即使没有 boardItem 也能返回想法

### 3. SnipRepository 原有批量查询方法

**文件**: `src/modules/material-mng/repositories/snip.repository.ts`

**已存在方法**:
```typescript
async findByIds(params: BatchQueryParams): Promise<Snip[]>
```

**说明**: 
- 原有方法返回完整内容，包含所有类型的完整 content 字段
- 新方法 `findByIdsWithContentTruncated` 专门用于看板列表场景，优化性能

### 4. BoardItemRepository 新增游标分页方法

**文件**: `src/modules/material-mng/repositories/board-item.repository.ts`

**新增方法**:

#### 3.1 游标分页查询（排除 BoardGroup）
```typescript
/**
 * 游标分页查询 - 排除 BoardGroup
 * 对应 youapp 的 selectByCursorExcludeGroup 方法
 */
async findByCursorExcludeGroup(
  boardId: string,
  limit: number = 10,
  startingAfter?: string,
): Promise<{ data: BoardItem[]; hasMore: boolean }>
```

**特点**:
- 支持游标分页，避免深度分页性能问题
- 自动排除 BoardGroup 类型的项目
- 返回 `hasMore` 标志，方便前端判断是否有更多数据

#### 3.2 统计方法（排除 BoardGroup）
```typescript
/**
 * 统计非 BoardGroup 的 BoardItem 数量
 * 对应 youapp 的 countExcludeGroup 方法
 */
async countExcludeGroup(boardId: string): Promise<number>
```

**特点**:
- 专门统计非 BoardGroup 类型的项目数量
- 与游标分页配合使用，提供准确的计数

### 5. BoardRepository 清理冗余方法

**文件**: `src/modules/material-mng/repositories/board.repository.ts`

**删除方法**:
- `getBoardItemsWithEntities` - 移至 BoardItemRepository
- `countNonGroupBoardItems` - 移至 BoardItemRepository

**原因**: 这些方法更适合放在 BoardItemRepository 中，符合单一职责原则。

## 使用示例

### 批量查询优化示例

```typescript
// 旧实现（N+1 查询）
for (const item of boardItems) {
  if (item.thoughtId) {
    const thought = await this.thoughtRepository.findById(item.thoughtId);
  }
}

// 新实现（批量查询，内容裁剪）
const snipIds = boardItems
  .filter(item => item.snipId)
  .map(item => item.snipId);
const thoughtIds = boardItems
  .filter(item => item.thoughtId)
  .map(item => item.thoughtId);

const [snips, thoughts] = await Promise.all([
  this.snipRepository.findByIdsWithContentTruncated({ ids: snipIds }),
  this.thoughtRepository.findByIds(thoughtIds)
]);

const snipMap = new Map(snips.map(s => [s.id, s]));
const thoughtMap = new Map(thoughts.map(t => [t.id, t]));
```

### 游标分页使用示例

```typescript
// 获取看板项目（排除组）
const { data: items, hasMore } = await this.boardItemRepository
  .findByCursorExcludeGroup(boardId, 10, lastItemId);

// 获取计数
const itemsCount = await this.boardItemRepository
  .countExcludeGroup(boardId);
```

## 下一步工作

1. **修改 Service 层**: 使用新的 Repository 方法实现批量查询逻辑
2. **优化 DTO 转换**: 避免在转换过程中进行额外的数据库查询
3. **添加缓存机制**: 对频繁查询的数据进行缓存

## 性能提升预期

通过这些 Repository 层的优化，预期可以：

1. **减少查询次数**: 从 O(N*M) 降至 O(N+3)
2. **提升响应速度**: 减少数据库往返次数
3. **支持大数据量**: 游标分页避免深度分页问题

## 注意事项

1. 所有新增方法都遵循 youapi 的命名规范和代码风格
2. 保持了与现有代码的兼容性
3. 添加了详细的中文注释说明