import {
  BoardIdDto,
  CreateBoardDto,
  DeleteBoardDto,
  GetBoardDto,
  MoveBoardItemToBoardGroupDto,
  MoveBoardItemToRootDto,
  UpdateBoardDto,
} from '@repo/api/generated-client/snake-case/index';
import { BoardItemTypeEnum } from '@repo/common/types/board-item';
// 转换后的类型
import { Board, BoardBaseInfo } from '@/typings/board';
import { BoardItem } from '@/typings/board-item';
import { APIResponse, apiClient, callAPI } from '@/utils/callHTTP';

// Board 基本操作

/**
 * 创建 Board
 */
export const createBoard = async (params: {
  name: string;
  description?: string;
  icon?: {
    name: string;
    color: string;
  };
}): Promise<APIResponse<BoardBaseInfo>> => {
  const createBoardDto: CreateBoardDto = {
    name: params.name,
    description: params.description || '',
    icon: params.icon || { name: 'Planet', color: '--primary' }, // 提供默认值
  };

  return callAPI(apiClient.boardApi.createBoard(createBoardDto));
};

/**
 * 更新 Board
 */
export const patchBoard = async (params: {
  id: string;
  name?: string;
  description?: string;
  icon?: {
    name: string;
    color: string;
  };
}): Promise<APIResponse<BoardBaseInfo>> => {
  const updateBoardDto: UpdateBoardDto = {
    ...params,
    description: params.description || '',
  };

  return callAPI(apiClient.boardApi.patchBoard(updateBoardDto));
};

/**
 * 删除 Board
 */
export const deleteBoard = async (boardId: string): Promise<APIResponse<void>> => {
  const deleteBoardDto: DeleteBoardDto = {
    id: boardId,
  };

  return callAPI(apiClient.boardApi.deleteBoard(deleteBoardDto));
};

/**
 * 获取 Board 详情
 * 后端的 board_items 挪到了 items 字段，跟列表接口保持一致
 */
export const getBoardDetail = async (boardId: string, abortSignal?: AbortSignal) => {
  const getBoardDto: GetBoardDto = {
    id: boardId,
  };

  const res = await callAPI(
    apiClient.boardApi.getBoardDetail(getBoardDto, { signal: abortSignal }),
  );

  if (res.data) {
    (res.data as unknown as Board).items = res.data.board_items as unknown as BoardItem[];
    (res.data as unknown as Board).items_count = res.data.board_items.filter(
      (item) => item.entity_type !== BoardItemTypeEnum.BOARD_GROUP,
    ).length;
  }

  return res as APIResponse<Board>;
};

/**
 * 列出所有 Boards（不包含 board items）
 */
export const listBoards = async () => {
  return callAPI(apiClient.boardApi.listBoards0({}));
};

/**
 * 列出带有部分 Board Items 的 Boards
 */
export const listBoardsWithSomeBoardItems = async () => {
  return (await callAPI(apiClient.boardApi.listBoardsWithSomeBoardItems())) as APIResponse<Board[]>;
};

/**
 * 归档 Board
 */
export const archiveBoard = async (boardId: string): Promise<APIResponse<BoardBaseInfo>> => {
  const boardIdDto: BoardIdDto = {
    id: boardId,
  };

  return callAPI(apiClient.boardApi.archiveBoard(boardIdDto));
};

/**
 * 取消归档 Board
 */
export const unarchiveBoard = async (boardId: string): Promise<APIResponse<BoardBaseInfo>> => {
  const boardIdDto: BoardIdDto = {
    id: boardId,
  };

  return callAPI(apiClient.boardApi.unarchiveBoard(boardIdDto));
};

// Board Item 操作

/**
 * 移动 BoardItem 到根目录
 */
export const moveBoardItemToRoot = async (params: {
  board_item_id: string;
  rank_after_id?: string;
}): Promise<APIResponse<void>> => {
  const moveBoardItemToRootDto: MoveBoardItemToRootDto = {
    board_item_id: params.board_item_id,
    rank_after_id: params.rank_after_id,
  };

  return callAPI(apiClient.boardItemApi.moveBoardItemToRoot(moveBoardItemToRootDto));
};

/**
 * 移动 BoardItem 到 BoardGroup
 */
export const moveBoardItemToBoardGroup = async (params: {
  board_item_id: string;
  parent_board_group_id: string;
  rank_after_id?: string;
}): Promise<APIResponse<void>> => {
  const moveBoardItemToBoardGroupDto: MoveBoardItemToBoardGroupDto = {
    board_item_id: params.board_item_id,
    parent_board_group_id: params.parent_board_group_id,
    rank_after_id: params.rank_after_id,
  };

  return callAPI(apiClient.boardItemApi.moveBoardItemToBoardGroup(moveBoardItemToBoardGroupDto));
};
