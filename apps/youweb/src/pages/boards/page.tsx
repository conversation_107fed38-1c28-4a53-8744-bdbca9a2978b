'use client';

import { useRequest } from 'ahooks';
import { useSet<PERSON>tom } from 'jotai';

import { Helmet } from 'react-helmet';

import { refreshBoardsAtom } from '@/hooks/useBoards';

import Container from './container';
import List from './list';
import SimpleLoading from './loading';

export default function BoardPage() {
  const refreshBoards = useSetAtom(refreshBoardsAtom);

  const { loading } = useRequest(refreshBoards);

  return (
    <Container>
      <Helmet>
        <title>YouMind Boards</title>
      </Helmet>
      {loading ? <SimpleLoading /> : <List />}
    </Container>
  );
}
