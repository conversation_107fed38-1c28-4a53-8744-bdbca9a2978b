@import "./globals-rich.css";

@import "../components/ai-ask/index.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  height: 100%;
}

@keyframes marquee {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(-100% - var(--gap)));
  }
}
@keyframes marquee-vertical {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(calc(-100% - var(--gap)));
  }
}

@keyframes loading-shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 250% 0;
  }
}

@keyframes shine {
  0% {
    background-position: 0% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  to {
    background-position: 0% 0%;
  }
}

:root {
  .ant-image-preview-operations {
    background-color: rgba(0, 0, 0, 0.22) !important;
  }

  /* 图片预览白色背景 */
  .ant-image-preview-img {
    background-color: rgba(255, 255, 255, 1) !important;
  }

  --text-quaternary: #989798;
  --text-primary: #0d0d0d;

  .dark {
    /* --text-quaternary: #989798;
    --text-primary: #0d0d0d; */
  }
  /* 这里不能用 hsl，会丢失渐变效果 */
  /* --text-quaternary: hsl(var(--caption));
  --text-primary: hsl(var(--foreground)); */

  .loading-shimmer {
    -webkit-text-fill-color: transparent;
    animation-delay: 0s;
    animation-duration: 2.5s;
    animation-iteration-count: infinite;
    animation-name: loading-shimmer;
    background: var(--text-quaternary)
      -webkit-gradient(
        linear,
        100% 0,
        0 0,
        from(var(--text-quaternary)),
        color-stop(0.5, var(--text-primary)),
        to(var(--text-quaternary))
      );
    -webkit-background-clip: text;
    background-clip: text;
    background-repeat: no-repeat;
    background-size: 50% 200%;
    display: inline-block;
  }

  [dir="ltr"] .loading-shimmer {
    background-position: -100% 0;
  }

  [dir="rtl"] .loading-shimmer {
    background-position: 200% 0;
  }

  .loading-shimmer:hover {
    -webkit-text-fill-color: var(--text-primary);
    animation: none;
  }

  [dir="ltr"] .loading-shimmer:hover {
    background: 0 0;
  }

  [dir="rtl"] .loading-shimmer:hover {
    background: 100% 0;
  }
}

@layer base {
  :root {
    --background: 240 12% 95%;
    --foreground: 0, 0%, 0%, 0.88;
    --global-background: 0 0 97% 1;

    --card: 0 0% 100%;
    --card-foreground: 0, 0%, 0%, 0.88;

    --popover: 0 0% 100%;
    --popover-foreground: 0, 0%, 0%, 0.88;

    --primary: 0, 0%, 0%, 0.88;
    --primary-foreground: 210 20% 98%;

    --secondary: 0, 0%, 65%;
    --secondary-foreground: 0 0% 0% / 0.6;

    --muted: 235, 86%, 5%, 0.07;
    --muted-foreground: 0, 0%, 0%, 0.6;

    --card-muted: 0, 0%, 100%, 0.12;

    --accent: 235, 86%, 5%, 0.04;
    --accent-foreground: 0, 0%, 0%, 0.88;

    --destructive: 3 100% 59%;
    --destructive-foreground: 0, 0%, 100%, 1;

    --inactive: 0, 0%, 100%, 0.64;

    --border: 240 1% 85%;
    --input: 235, 86%, 5%, 0.16;
    --ring: 240 10% 3.9%;
    --divider: 235, 86%, 5%, 0.12;

    --radius: 0.5rem;

    /** YouMind Custom CSS Variables **/
    --card-snips: 235, 86%, 5%, 0.04;
    --caption: 0, 0%, 0%, 0.4;
    --disabled: 0, 0%, 0%, 0.24;
    --primary-outline: 126 60% 58%;
    --primary-outline-foreground: 126 60% 58%;

    --block-background: 0, 0%, 100%;

    --brand: 0 0% 12%;
    --brand-hover: 0 0% 40%;
    --brand-pressed: 0 0% 0%;
    --brand-disabled: 0 0% 0% 76%;
    --brand-foreground: 0 0% 100%;

    --active: 0 0% 100%;
    --active-foreground: 235, 86%, 5%, 0.16;

    --error: 353, 87%, 67%, 1;

    --dark-blue-color: #3070f0;
    --blue-color: #61c5ff;
    --purple-color: #c39efd;
    --green-color: #9deb63;
    --red-color: #ff999c;
    --yellow-color: #ffdc52;
    --orange-color: #ff9500;

    --action-primary: var(--muted-foreground);
    --action-secondary: var(--caption);
    --action-primary-active: 0, 0%, 100%, 0.88;
    --action-secondary-active: 0, 0%, 100%, 0.6;
    --action-primary-active-note: 0, 0%, 14%, 0.88;
    --action-secondary-active-note: 0, 0%, 14%, 0.6;
    --link: 213, 100%, 43%;

    /* Board 的 Icon 颜色 */
    --function-gray: 240 2% 57%;
    --function-link: 211 100% 50%;
    --function-mint: 175 60% 60%;
    --function-green: 129 67% 48%;
    --function-indigo: 240 57% 58%;
    --function-purple: 274 63% 60%;
    --function-pink: 324 80% 55%;
    --function-red: 4 80% 59%;
    --function-orange: 35 100% 50%;
    --function-yellow: 48 100% 50%;
    --function-brown: 33 73% 29%;

    /* Assistant 的头像 */
    --assistant-avatar-icon-red: 4 80% 59%;
    --assistant-avatar-icon-brown: 40 72% 44%;
    --assistant-avatar-icon-yellow: 44 100% 49%;
    --assistant-avatar-icon-green: 137 53% 34%;
    --assistant-avatar-icon-blue: 217 100% 61%;
    --assistant-avatar-icon-purple: 274 63% 60%;
    --assistant-avatar-icon-pink: 321 100% 81%;
    --assistant-avatar-icon-gray: 43 6% 42%;

    --assistant-avatar-emoji-red: 0 95% 92%;
    --assistant-avatar-emoji-brown: 36 100% 88%;
    --assistant-avatar-emoji-yellow: 53 100% 84%;
    --assistant-avatar-emoji-green: 104 37% 92%;
    --assistant-avatar-emoji-blue: 223 100% 90%;
    --assistant-avatar-emoji-purple: 268 100% 93%;
    --assistant-avatar-emoji-pink: 318 87% 94%;
    --assistant-avatar-emoji-gray: 43 33% 93%;

    /* 黑白版本的 Assistant 的头像 */
    --assistant-icon: 210, 5%, 24%, 1;

    --tweet-container-margin: 1.5rem 0;
    --tweet-header-font-size: 0.9375rem;
    --tweet-header-line-height: 1.25rem;
    --tweet-body-font-size: 1.25rem;
    --tweet-body-font-weight: 400;
    --tweet-body-line-height: 1.5rem;
    --tweet-body-margin: 0;
    --tweet-quoted-container-margin: 0.75rem 0;
    --tweet-quoted-body-font-size: 0.938rem;
    --tweet-quoted-body-font-weight: 400;
    --tweet-quoted-body-line-height: 1.25rem;
    --tweet-quoted-body-margin: 0.25rem 0 0.75rem 0;
    --tweet-info-font-size: 0.9375rem;
    --tweet-info-line-height: 1.25rem;
    --tweet-actions-font-size: 0.875rem;
    --tweet-actions-line-height: 1rem;
    --tweet-actions-font-weight: 500;
    --tweet-actions-icon-size: 1.25em;
    --tweet-actions-icon-wrapper-size: calc(var(--tweet-actions-icon-size) + 0.75em);
    --tweet-replies-font-size: 0.875rem;
    --tweet-replies-line-height: 1rem;
    --tweet-replies-font-weight: 500;
    --tweet-skeleton-gradient: linear-gradient(270deg, #fafafa, #eaeaea, #eaeaea, #fafafa);
    --tweet-border: 1px solid #cfd9de;
    --tweet-font-family:
      -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    --tweet-font-color: #0f1419;
    --tweet-font-color-secondary: #536471;
    --tweet-bg-color: #fff;
    --tweet-bg-color-hover: #f7f9f9;
    --tweet-quoted-bg-color-hover: rgba(0, 0, 0, 0.03);
    --tweet-color-blue-primary: #1d9bf0;
    --tweet-color-blue-primary-hover: #1a8cd8;
    --tweet-color-blue-secondary: #006fd6;
    --tweet-color-blue-secondary-hover: rgba(0, 111, 214, 0.1);
    --tweet-color-red-primary: #f91880;
    --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);
    --tweet-color-green-primary: #00ba7c;
    --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);
    --tweet-twitter-icon-color: var(--tweet-font-color);
    --tweet-verified-old-color: #829aab;
    --tweet-verified-blue-color: var(--tweet-color-blue-primary);
    --color-1: 0 100% 63%;
    --color-2: 270 100% 63%;
    --color-3: 210 100% 63%;
    --color-4: 195 100% 63%;
    --color-5: 90 100% 63%;
  }

  .dark {
    --background: 210 6% 13%;
    --foreground: 0, 0%, 100%, 0.88;

    --global-background: 236 12% 12% 1;

    --card: 0 0% 10%;
    --card-foreground: 0, 0%, 100%, 0.88;

    --popover: 0 0% 18%;
    --popover-foreground: 0, 0%, 100%, 0.88;

    --primary: 0, 0%, 100%, 0.88;
    --primary-foreground: 0, 0%, 0%, 0.88;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 100% 0.6;

    --muted: 0, 0%, 100%, 0.1;
    --muted-foreground: 0, 0%, 100%, 0.6;

    --accent: 0, 0%, 100%, 0.06;
    --accent-foreground: 0, 0%, 100%, 0.88;

    --destructive: 3 100% 61%;
    --destructive-foreground: 0, 0%, 100%, 1;

    --inactive: 0, 0%, 10%, 0.64;

    --border: 240 1% 85%;
    --input: 0, 0%, 100%, 0.2;
    --ring: 240 4.9% 83.9%;
    --divider: 0, 0%, 100%, 0.16;

    /** YouMind Custom CSS Variables **/
    --card-snips: 0, 0%, 100%, 0.06;
    --caption: 0, 0%, 100%, 0.4;
    --disabled: 0, 0%, 100%, 0.3;

    --primary-outline: 128 49% 59%;
    --primary-outline-foreground: 128 49% 59%;

    --block-background: 0, 0%, 14%;

    --brand: 0 0% 100%;
    --brand-hover: 0 0% 90%;
    --brand-pressed: 240 1% 65%;
    --brand-disabled: 180 1% 39%;
    --brand-foreground: 0 0% 12%;

    --active: 0 0% 26%;
    --active-foreground: 0, 0%, 100%, 0.88;

    --error: 353, 87%, 67%, 1;

    --action-primary: var(--foreground);
    --action-secondary: var(--caption);

    /* Board 的 Icon 颜色 */
    --function-gray: 240 2% 57%;
    --function-link: 211 100% 50%;
    --function-mint: 175 60% 60%;
    --function-green: 129 67% 48%;
    --function-indigo: 240 57% 58%;
    --function-purple: 274 63% 60%;
    --function-pink: 324 80% 55%;
    --function-red: 4 80% 59%;
    --function-orange: 35 100% 50%;
    --function-yellow: 48 100% 50%;
    --function-brown: 33 73% 29%;

    /* 黑白版本的 Assistant 的头像 */
    --assistant-icon: 0, 0%, 100%, 1;

    --tweet-skeleton-gradient: linear-gradient(270deg, #15202b, #1e2732, #1e2732, #15202b);
    --tweet-border: 1px solid #425364;
    --tweet-font-family:
      -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    --tweet-font-color: #f7f9f9;
    --tweet-font-color-secondary: #8b98a5;
    --tweet-bg-color: #15202b;
    --tweet-bg-color-hover: #1e2732;
    --tweet-quoted-bg-color-hover: hsla(0, 0%, 100%, 0.03);
    --tweet-color-blue-primary: #1d9bf0;
    --tweet-color-blue-primary-hover: #1a8cd8;
    --tweet-color-blue-secondary: #6bc9fb;
    --tweet-color-blue-secondary-hover: rgba(107, 201, 251, 0.1);
    --tweet-color-red-primary: #f91880;
    --tweet-color-red-primary-hover: rgba(249, 24, 128, 0.1);
    --tweet-color-green-primary: #00ba7c;
    --tweet-color-green-primary-hover: rgba(0, 186, 124, 0.1);
    --tweet-twitter-icon-color: var(--tweet-font-color);
    --tweet-verified-old-color: #829aab;
    --tweet-verified-blue-color: #fff;
    --color-1: 0 100% 63%;
    --color-2: 270 100% 63%;
    --color-3: 210 100% 63%;
    --color-4: 195 100% 63%;
    --color-5: 90 100% 63%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-global-background text-foreground !important;
  }

  h1,
  .font-h1 {
    font-size: 4.25rem;
    line-height: 1.2;
    @apply font-medium;
  }

  h2,
  .font-h2 {
    font-size: 2.5rem;
    line-height: 1.2;
    @apply font-medium;
  }

  h3,
  .font-h3 {
    font-size: 2rem;
    line-height: 1.2;
    @apply font-medium;
  }

  h4,
  .font-h4 {
    font-size: 1.75rem;
    line-height: 1.4;
    @apply font-medium;
  }

  h5,
  .font-h5 {
    font-size: 1.5rem;
    line-height: 1.4;
    @apply font-medium;
  }

  h6,
  .font-h6,
  .font-title {
    font-size: 1.25rem;
    line-height: 1.5;
    @apply font-medium;
  }

  .font-subtitle {
    font-size: 1rem;
    line-height: 1.5;
    @apply font-medium;
  }

  .font-paragraph {
    font-size: 1rem;
    line-height: 1.5;
    @apply font-normal;
  }

  .font-body {
    font-size: 0.875rem;
    line-height: 1.25rem;
    @apply font-normal;
  }

  .font-caption {
    font-size: 0.75rem;
    line-height: 1.125rem;
    @apply font-normal;
    @apply text-caption;
  }

  .font-caption-s {
    font-size: 0.625rem;
    line-height: 0.875rem;
    @apply font-normal;
    @apply text-caption;
  }

  .inverted-img {
    filter: invert(1);
  }

  .dark .inverted-img {
    filter: invert(0);
  }

  .reversed-img {
    filter: invert(0);
  }

  .dark .reversed-img {
    filter: invert(1);
  }
}

@layer components {
  /**
   * Typography
   */
  .footnote {
    @apply text-xs;
    line-height: 18px;
    font-weight: 400;
  }

  .caption {
    @apply text-xs;
    line-height: 18px;
    font-weight: 400;
  }

  .body {
    @apply text-sm;
    line-height: 20px;
    font-weight: 400;
  }

  .body-bold {
    @apply text-sm;
    line-height: 20px;
    font-weight: 500;
  }

  .body-strong {
    @apply text-sm;
    line-height: 20px;
    font-weight: 500;
  }

  .paragraph {
    @apply text-base;
    line-height: 26px;
    font-weight: 400;
  }

  .title {
    @apply text-base;
    line-height: 24px;
    font-weight: 500;
  }

  .title-large {
    @apply text-lg;
    font-size: 20px;
    line-height: 28px;
    font-weight: 590;
  }

  .headline3 {
    @apply text-2xl;
    line-height: 32px;
    font-weight: 500;
  }

  .headline2 {
    @apply text-2xl;
    line-height: 36px;
    font-weight: 500;
  }

  .headline1 {
    @apply text-3xl;
    line-height: 40px;
    font-weight: 500;
  }

  .display {
    @apply text-4xl;
    line-height: 48px;
    font-weight: 500;
  }

  .display-medium {
    font-size: 44px;
    font-style: normal;
    font-weight: 590;
    line-height: 52px;
  }

  .display-large {
    @apply text-5xl;
    line-height: 80px;
    font-weight: 500;
  }

  .text-ellipsis-line-1 {
    @apply overflow-hidden text-ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .text-ellipsis-line-2 {
    @apply overflow-hidden text-ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .text-ellipsis-line-3 {
    @apply overflow-hidden text-ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  .text-ellipsis-line-4 {
    @apply overflow-hidden text-ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
  }

  .overview-container {
    p {
      margin-bottom: 8px;
    }

    .ym-citation-btn {
      width: 12px;
      height: 12px;
      border-radius: 50%;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      /* background-color: hsl(var(--muted-foreground)); */
      background: #02041a14;
      margin-left: 4px;
    }

    & p .ym-citation-btn {
      display: none;
    }

    & > p {
      color: hsl(var(--secondary-foreground));
    }

    h3 {
      font-size: 14px;
      font-weight: 700;
      line-height: 22px;
      margin-bottom: 16px;
      margin-top: 16px;
      color: hsl(var(--foreground));
    }

    div.sc {
      border-radius: 12px;
      /* background-color: hsl(var(--muted)); */
      /* padding: 12px 16px; */
      margin-bottom: 16px;
      color: hsl(var(--secondary-foreground));
    }

    h4 {
      font-size: 14px;
      font-weight: 400;
      color: hsl(var(--foreground));
      margin-bottom: 8px;
    }

    ul,
    ol {
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      padding: 8px 24px;
      padding-top: 0px;
      padding-right: 0px;
      list-style: disc;
    }

    li {
      margin-left: 8px;
      padding-right: 16px;
    }
  }

  .block-media-grid-sizer,
  .block-media-grid-item {
    width: 33.3333%;
  }

  /* Hide scrollbar for Chrome, Safari, and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge, and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  * {
    outline: none !important;
  }

  .smaller-h {
    h1 {
      font-size: 1.6em;
      line-height: 1.25em;
    }

    h2 {
      font-size: 1.2em;
      line-height: 1.51em;
    }

    h3 {
      font-size: 1em;
      line-height: 1.66em;
    }
  }

  .text-snippet-card-content {
    h1 {
      font-size: 14px;
      line-height: 22px;
      margin: 8px 0;
      font-weight: 600;
    }

    h2 {
      font-size: 14px;
      line-height: 22px;
      margin: 8px 0;
      font-weight: 500;
    }

    h3 {
      font-size: 14px;
      line-height: 22px;
      margin: 8px 0;
    }

    p {
      margin: 6px 0;
    }
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  .theme {
    --duration: 8s;
    --animate-shine: shine var(--duration) infinite linear;
  }
}

/* 使用 CSS 变量 */
:root {
  --scrollbar-thumb: rgba(0, 0, 0, 0.1);
  --scrollbar-thumb-hover: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar {
  width: 4px; /* 修改滚动条宽度,默认是16px */
  height: 4px; /* 设置横向滚动条的高度 */
}

::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-thumb-hover);
}

/* Firefox */
* {
  scrollbar-color: var(--scrollbar-thumb) transparent;
}
