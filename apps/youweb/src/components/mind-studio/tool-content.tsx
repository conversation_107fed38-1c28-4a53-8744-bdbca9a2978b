import { useAtomValue } from 'jotai';
import { activeToolIdAtom } from '@/hooks/useMindStudio';
import { cn } from '@/utils/utils';

import { toolRegistry } from './tools';

interface ToolContentProps {
  className?: string;
}

export function ToolContent({ className }: ToolContentProps) {
  const activeToolType = useAtomValue(activeToolIdAtom);

  return (
    <div
      className={cn(
        'min-h-0 w-full flex-1 overflow-y-auto overflow-x-hidden px-6 pb-[2px] pt-5',
        className,
      )}
    >
      {toolRegistry.map((toolConfig) => {
        const renderComponent = toolConfig.component;
        if (!renderComponent) {
          return null;
        }
        const isPopup = toolConfig.isPopup;
        if (isPopup) {
          return null;
        }

        return (
          <div
            key={toolConfig.type}
            className={cn('h-full w-full')}
            style={{
              display: activeToolType === toolConfig.type ? 'block' : 'none',
            }}
          >
            {renderComponent()}
          </div>
        );
      })}
    </div>
  );
}
