import { AssistantTypeEnum } from '@repo/common/types/assistant/types';
import { Button } from '@repo/ui/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@repo/ui/components/ui/popover';

import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { motion } from 'framer-motion';
import { useAtom, useAtomValue } from 'jotai';
import React, { useState } from 'react';
import { activeToolIdAtom, setActiveToolAtom } from '@/hooks/useMindStudio';
import { cn } from '@/utils/utils';
import { ToolConfig, toolRegistry } from './tools';

interface ToolBarProps {
  className?: string;
}

export function ToolBar(_props: ToolBarProps) {
  return (
    <div className="px-6 pt-2">
      <div className="flex items-center pb-1 border-b border-muted">
        <ToolList />
      </div>
    </div>
  );
}

function ToolList() {
  const activeToolType = useAtomValue(activeToolIdAtom);
  const [, setActiveTool] = useAtom(setActiveToolAtom);
  const { trackButtonClick } = useTrackActions();

  const [activePopupTool, setActivePopupTool] = useState<ToolConfig | null>(null);

  const handleToolClick = (toolConfig: ToolConfig) => {
    // 上报埋点
    trackButtonClick('ms_tool_click', {
      tool_name: toolConfig.name,
    });
    if (toolConfig.isPopup) {
      setActivePopupTool(toolConfig);
      return;
    }
    setActiveTool(toolConfig.type);
  };

  const renderToolButton = (toolConfig: ToolConfig) => {
    let toolName: React.ReactNode | null = (
      <motion.div
        initial={{ width: 0, opacity: 0 }}
        animate={{ width: 'auto', opacity: 1 }}
        transition={{ duration: 0.1 }}
      >
        <div className={cn('max-w-[80px] truncate text-nowrap text-xs')}>{toolConfig.name}</div>
      </motion.div>
    );

    if (toolConfig.type === AssistantTypeEnum.TRANSLATOR) {
      toolName = null;
    }

    return (
      <Button
        key={toolConfig.type}
        onClick={() => handleToolClick(toolConfig)}
        variant={activeToolType === toolConfig.type ? 'secondary' : 'ghost'}
        size="sm"
        className={cn(
          'group flex shrink-0 items-center justify-center pl-[6px] pr-2 transition-all duration-100',
          activeToolType === toolConfig.type
            ? 'h-6 bg-muted text-foreground'
            : 'h-6 bg-transparent text-muted-foreground hover:bg-muted hover:text-foreground',
        )}
      >
        <div className={cn('flex flex-row items-center justify-center gap-1')}>
          {toolConfig.icon(toolConfig, 'flex justify-center')}
          {toolName}
        </div>
      </Button>
    );
  };

  const renderToolItem = (toolConfig: ToolConfig) => {
    if (toolConfig.isPopup) {
      const renderComponent = toolConfig.component;
      if (!renderComponent) {
        return null;
      }
      return (
        <Popover
          open={activePopupTool?.type === toolConfig.type}
          onOpenChange={(open) => {
            if (!open) {
              setActivePopupTool(null);
            } else {
              setActivePopupTool(toolConfig);
            }
          }}
        >
          <PopoverTrigger asChild>{renderToolButton(toolConfig)}</PopoverTrigger>
          <PopoverContent>{renderComponent()}</PopoverContent>
        </Popover>
      );
    }
    return renderToolButton(toolConfig);
  };

  return (
    <div className="flex min-w-0 flex-1 items-center gap-[2px] overflow-auto">
      {toolRegistry.map((toolConfig) => {
        return <React.Fragment key={toolConfig.type}>{renderToolItem(toolConfig)}</React.Fragment>;
      })}
    </div>
  );
}
