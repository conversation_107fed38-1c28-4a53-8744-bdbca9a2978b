import { AnimatePresence, motion } from 'framer-motion';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import { useEffect, useRef, useState } from 'react';
import { getBoardSidebarActiveAtom, getBoardSidebarWidthAtom } from '@/hooks/useBoardState';
import {
  checkMindStudioAndOpenIfNeededAtom,
  chooseFirstToolAtom,
  chooseFirstToolIfNeededAtom,
  DEFAULT_MIND_STUDIO_WIDTH,
  getMindStudioActiveAtom,
  getMindStudioWidthAtom,
  MIN_MIND_STUDIO_WIDTH,
  mindStudioWidthDraggingAtom,
  setMindStudioActiveAtom,
  setMindStudioWidthAtom,
} from '@/hooks/useMindStudio';
import { cn } from '@/utils/utils';
import { ResizeHandle } from '../resizer';
// import { useBoardTranslationEffect } from '../translate/hooks/useBoardTranslation';
import { MindMaterialView } from './mind-material';
import { MindStudio } from './mind-studio';
import { MindSwitchButton } from './mind-switch-button';

interface MindWorkspaceProps {
  title?: React.ReactNode;
  actions: React.ReactNode;
  className?: string;
  headerClassName?: string;
  boardId: string;
  placeholder?: React.ReactNode;
  loading?: boolean;
  showMaterialContainer?: boolean;
  useMaxWidth?: boolean;
  viewClassName?: string;
  hideHeader?: boolean;
  hideMindStudio?: boolean;
}

export function MindWorkspace({
  title,
  actions,
  className,
  headerClassName,
  boardId,
  placeholder,
  loading,
  showMaterialContainer = true,
  useMaxWidth = true,
  viewClassName,
  hideHeader = false,
  hideMindStudio = false,
}: MindWorkspaceProps) {
  const getMindStudioActive = useAtomValue(getMindStudioActiveAtom);
  const checkMindStudioAndOpenIfNeeded = useSetAtom(checkMindStudioAndOpenIfNeededAtom);
  const _setMindStudioActive = useSetAtom(setMindStudioActiveAtom);
  const _chooseFirstToolIfNeeded = useSetAtom(chooseFirstToolIfNeededAtom);
  const chooseFirstTool = useSetAtom(chooseFirstToolAtom);
  const getBoardSidebarWidth = useAtomValue(getBoardSidebarWidthAtom);
  const getBoardSidebarActive = useAtomValue(getBoardSidebarActiveAtom);
  const getMindStudioWidth = useAtomValue(getMindStudioWidthAtom);
  const setMindStudioWidth = useSetAtom(setMindStudioWidthAtom);
  const [mindStudioWidthDragging, setMindStudioWidthDragging] = useAtom(
    mindStudioWidthDraggingAtom,
  );
  const [mindStudioInAnimation, setMindStudioInAnimation] = useState(false);

  const startXRef = useRef(-1);

  // useEffect(() => {
  //   if (user?.id && mindStudioToolState.userId) {
  //     if (user.id !== mindStudioToolState.userId) {
  //       resetMindStudioToolState();
  //     }
  //   }
  // }, []);

  useEffect(() => {
    // 顺序不能换，先用第一个
    chooseFirstTool();
  }, []);

  useEffect(() => {
    if (!loading) {
      checkMindStudioAndOpenIfNeeded(boardId);
    }
  }, [loading, checkMindStudioAndOpenIfNeeded, boardId]);

  // useBoardTranslationEffect();

  const mindMaterial = (
    <div className={cn('h-full min-w-0 flex-grow', !getMindStudioActive(boardId) && 'mr-3')}>
      <MindMaterialView
        boardId={boardId}
        placeholder={placeholder}
        loading={loading}
        showMaterialContainer={showMaterialContainer}
        useMaxWidth={useMaxWidth}
        className={viewClassName}
        hideHeader={hideHeader}
      />
    </div>
  );

  const mindStudio = (
    <div
      className={cn('group/mind-studio relative h-full')}
      id="mind-studio-container"
      style={{
        width:
          getMindStudioActive(boardId!) && !mindStudioInAnimation
            ? getMindStudioWidth(boardId!)
            : '',
      }}
    >
      <AnimatePresence>
        {getMindStudioActive(boardId!) && !loading && (
          <>
            <motion.div
              className="relative h-full"
              onAnimationStart={() => {
                setMindStudioInAnimation(true);
              }}
              onAnimationComplete={() => {
                setMindStudioInAnimation(false);
              }}
              style={{ width: getMindStudioWidth(boardId!) }}
              {...(!mindStudioWidthDragging
                ? {
                    initial: { width: 0 },
                    animate: { width: getMindStudioWidth(boardId!) },
                    exit: { width: 0 },
                    transition: { duration: 0.2 },
                  }
                : {})}
            >
              <MindStudio />
            </motion.div>
            <ResizeHandle
              className={cn(
                'absolute -left-[8px] top-1 z-10 h-[calc(100%-8px)] opacity-0',
                mindStudioWidthDragging && 'opacity-60',
              )}
              onResetWidth={() => {
                setMindStudioWidth(boardId, DEFAULT_MIND_STUDIO_WIDTH);
              }}
              onSizeChange={(deltaX) => {
                // 最小宽度 400
                setMindStudioWidth(
                  boardId,
                  Math.min(
                    Math.max(MIN_MIND_STUDIO_WIDTH, startXRef.current + deltaX),
                    window.innerWidth -
                      (getBoardSidebarActive(boardId) ? getBoardSidebarWidth(boardId) : 0) -
                      MIN_MIND_STUDIO_WIDTH,
                  ),
                );
              }}
              handleStartDragging={() => {
                setMindStudioWidthDragging(true);
                startXRef.current = getMindStudioWidth(boardId);
              }}
              handleStopDragging={() => {
                setTimeout(() => {
                  setMindStudioWidthDragging(false);
                }, 300);
                startXRef.current = -1;
              }}
            />
          </>
        )}
      </AnimatePresence>
    </div>
  );

  const header = (
    <div
      className={cn(
        'flex h-[56px] flex-shrink-0 flex-row items-center justify-between py-3 pr-6 transition-all duration-300',
        headerClassName,
      )}
    >
      <div className="flex flex-row items-center">{title}</div>
      <div className="flex flex-row items-center gap-2">
        {actions}
        <MindSwitchButton />
      </div>
    </div>
  );

  return (
    <div className={cn('flex h-full flex-col', className)}>
      {!hideHeader && header}
      <div className={cn('flex min-h-0 flex-grow flex-row transition-all duration-300')}>
        {mindMaterial}
        {!hideMindStudio && mindStudio}
      </div>
    </div>
  );
}
