import type { AssistantTypeEnum } from '@repo/common/types/assistant/types';
import type { ReactNode } from 'react';

export interface ToolConfig {
  type: AssistantTypeEnum;
  component: () => React.ReactNode;
  // 内置工具的图标渲染函数
  icon: (tool: ToolConfig, className?: string) => ReactNode;
  // 工具名称
  name: string;
  description?: string;
  // 三个点右键菜单，如有需要，则自行声明三个点里唤起的内容
  moreMenu?: (tool: ToolConfig, onClose: () => void) => ReactNode;
  // 是否是官方工具
  isOfficial?: boolean;
  // 是否是浮窗型的
  isPopup?: boolean;
}
