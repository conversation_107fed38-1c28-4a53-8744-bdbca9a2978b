import { AssistantIconTypeEnum, AssistantTypeEnum } from '@repo/common/types/assistant/types';
import { AnimatePresence, motion } from 'framer-motion';
import { useAtomValue } from 'jotai';
import { Voice } from '@/components/icon/action/voice';
// import TranslatorTool from "@/components/panel/translator";
import { useBoardPlaylistItems } from '@/components/mind-studio/panel/listen/hooks/useBoardPlaylistItems';
import { AssistantAvatar } from '@/components/new-assistant-page/components/AssistantAvatar';
import { activeToolIdAtom } from '@/hooks/useMindStudio';
import ChatAssistant from './chat';
import { ListenTool } from './listen';
import { NoteTool } from './note';
import { ReaderTool } from './reader';
import { ToolConfig } from './types';
export type { ToolConfig };

export const toolRegistry: ToolConfig[] = [
  {
    type: AssistantTypeEnum.CHAT,
    icon: (_tool, className) => (
      <AssistantAvatar
        avatarData={{
          type: AssistantIconTypeEnum.BUILT_IN,
          value: 'MagicSlug',
          bg_color: '',
        }}
        iconSize={14}
        className={className}
      />
    ),
    component: () => <ChatAssistant />,
    name: 'Chat',
    isOfficial: true,
  },
  {
    type: AssistantTypeEnum.NOTE,
    icon: (_tool, className) => (
      <AssistantAvatar
        avatarData={{
          type: AssistantIconTypeEnum.BUILT_IN,
          value: 'Firefly',
          bg_color: '',
        }}
        iconSize={14}
        className={className}
      />
    ),
    component: () => <NoteTool />,
    name: 'Notes',
    isOfficial: true,
  },
  {
    type: AssistantTypeEnum.LISTEN,
    icon: function ListenIcon(tool, className) {
      const { audioPlayerState } = useBoardPlaylistItems();
      const isSoundPlaying = audioPlayerState.sound?.playing();
      const activeTool = useAtomValue(activeToolIdAtom);

      return (
        <>
          <AssistantAvatar
            avatarData={{
              type: AssistantIconTypeEnum.BUILT_IN,
              value: 'Dolphin',
              bg_color: '',
            }}
            iconSize={14}
            className={className}
          />

          <AnimatePresence>
            {isSoundPlaying && activeTool !== tool.type && (
              <motion.div
                initial={{ opacity: 0, width: 0 }}
                animate={{ opacity: 1, width: 16 }}
                exit={{ opacity: 0, width: 0 }}
                className="ml-[-6px] mr-[6px] flex h-4 w-4 items-center justify-center rounded-full"
                style={{
                  backgroundColor: 'rgba(0, 122, 255, 0.16)',
                }}
              >
                <Voice size={10} className="text-link" animate />
              </motion.div>
            )}
          </AnimatePresence>
        </>
      );
    },
    component: () => <ListenTool />,
    name: 'Listen',
    isOfficial: true,
  },
  {
    type: AssistantTypeEnum.READER,
    icon: (_tool, className) => (
      <AssistantAvatar
        avatarData={{
          type: AssistantIconTypeEnum.BUILT_IN,
          value: 'Owl',
          bg_color: '',
        }}
        iconSize={14}
        className={className}
      />
    ),
    component: () => <ReaderTool />,
    name: 'Reader',
    isOfficial: true,
  },
];
