// import { DropdownMenuTrigger } from '@repo/ui/components/ui/dropdown-menu';
// import { Input } from '@repo/ui/components/ui/input';
// import { useAtom } from 'jotai';
// import { ChevronDown } from 'lucide-react';

// import { avatarDataAtom, nameAtom } from '../../hooks/useNewAssistant';
// import { AssistantAvatar } from '../AssistantAvatar';
// import AssistantAvatorSelector from '../AssistantAvatorSelector';

// export const NameAndAvatar = () => {
//   const [name, setName] = useAtom(nameAtom);
//   const [avatarData, setAvatarData] = useAtom(avatarDataAtom);

//   return (
//     <div className="flex items-center justify-between gap-4">
//       <div className="flex-1">
//         <div className="mb-1 text-sm font-medium text-foreground">Name</div>
//         <Input
//           value={name}
//           onChange={(e) => setName(e.target.value)}
//           placeholder="New assistant"
//           className="pl-0 text-2xl font-medium bg-transparent border-none text-foreground placeholder:text-border"
//         />
//       </div>

//       <AssistantAvatorSelector avatarData={avatarData} onChange={setAvatarData}>
//         <DropdownMenuTrigger>
//           <div className="flex cursor-pointer items-center gap-[6px]">
//             <AssistantAvatar
//               avatarData={avatarData}
//               className="flex h-[56px] w-[56px] items-center justify-center rounded-full border border-divider bg-card-snips text-[28px] text-assistant-icon"
//             />
//             <div className="text-caption-foreground">
//               <ChevronDown size={16} />
//             </div>
//           </div>
//         </DropdownMenuTrigger>
//       </AssistantAvatorSelector>
//     </div>
//   );
// };
