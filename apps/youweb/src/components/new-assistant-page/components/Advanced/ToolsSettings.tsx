// import { TOOL_TYPES } from '@repo/common/consts/tool/const';
// import { Checkbox } from '@repo/ui/components/ui/checkbox';
// import { useAtom } from 'jotai';

// import { toolsAtom } from '../../hooks/useNewAssistant';

// type CustomAssistantTool = {
//   name: string;
//   toolType: TOOL_TYPES;
//   description: string;
// };

// const toolsData: CustomAssistantTool[] = [
//   {
//     name: 'Search the web',
//     toolType: TOOL_TYPES.GOOGLE_SEARCH,
//     description: 'Allow the assistant to search the web.',
//   },
//   {
//     name: 'Create image',
//     toolType: TOOL_TYPES.IMAGE_GENERATE,
//     description: 'Allow the assistant to create images.',
//   },
//   {
//     name: 'Create diagram',
//     toolType: TOOL_TYPES.DIAGRAM_GENERATE,
//     description: 'Allow the assistant to turn concepts into diagrams.',
//   },
// ];

// export const ToolsSettings = () => {
//   const [tools, setTools] = useAtom(toolsAtom);

//   return (
//     <>
//       <div className="mt-3 text-sm font-medium">Tools</div>
//       <div className="mb-2 text-xs text-caption-foreground">
//         Choose the tools this assistant needs.
//       </div>
//       <div className="flex flex-col w-full gap-3">
//         {toolsData.map((tool) => (
//           <label
//             key={tool.toolType}
//             htmlFor={`tool-${tool.toolType}`}
//             className="flex items-center justify-between gap-4 transition-all cursor-pointer"
//           >
//             <Checkbox
//               id={`tool-${tool.toolType}`}
//               checked={tools[tool.toolType]}
//               onCheckedChange={(checked) => setTools({ ...tools, [tool.toolType]: checked })}
//             />
//             <div className="flex-1">
//               <div className="text-sm font-medium text-foreground">{tool.name}</div>
//               {/* <div className="text-xs text-caption-foreground">
//                 {tool.description}
//               </div> */}
//             </div>
//           </label>
//         ))}
//       </div>
//     </>
//   );
// };
