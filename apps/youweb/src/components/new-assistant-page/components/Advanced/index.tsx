// import { ASK_AI_MODELS, MODEL_DEFINITION } from '@repo/common/types/chat/enum';
// import { Bot, Check, ChevronDown } from 'lucide-react';
// import { useState } from 'react';
// import { DevOnly } from '@/components/DevOnly';
// import { Slider } from '@/components/ui/slider';
// import { cn } from '@/utils/utils';

// import { type SupporttedModel, useNewAssistant } from '../../hooks/useNewAssistant';
// import { OperationMode } from './OperationMode';
// import { ToolsSettings } from './ToolsSettings';

// export const Advanced = () => {
//   const { selectedModel, generating, setSelectedModel, temperature, setTemperature } =
//     useNewAssistant();

//   const [showAdvanced, setShowAdvanced] = useState(true);

//   // 获取模型定义信息
//   const getModelInfo = (model: SupporttedModel) => {
//     const modelDef = MODEL_DEFINITION.get(model);

//     return {
//       icon_url: modelDef?.icon_url || '',
//       title: modelDef?.title || model,
//     };
//   };

//   return (
//     <div className={cn('flex w-full flex-col', generating && 'pointer-events-none opacity-50')}>
//       <div
//         className="flex cursor-pointer items-center gap-1"
//         onClick={() => setShowAdvanced(!showAdvanced)}
//       >
//         <span className="text-sm font-medium">Advanced</span>
//         <ChevronDown
//           size={16}
//           className={cn(showAdvanced && 'rotate-180', 'transition-transform')}
//         />
//       </div>

//       {showAdvanced ? (
//         <>
//           <div className="mt-4 text-sm font-medium">Model</div>
//           <div className="text-xs text-caption-foreground">
//             Choose the model that will power this assistant.
//           </div>
//           <div
//             className="mt-3 flex w-full gap-3 overflow-x-auto pb-3"
//             style={{ flexWrap: 'nowrap' }}
//           >
//             {ASK_AI_MODELS.map((model) => {
//               const modelInfo = getModelInfo(model);

//               return (
//                 <div
//                   key={model}
//                   onClick={() => setSelectedModel(model)}
//                   className={cn(
//                     'relative flex h-[92px] w-[120px] min-w-[120px] cursor-pointer flex-col justify-between rounded-[8px] bg-muted p-3 transition-all hover:opacity-80',
//                   )}
//                 >
//                   {selectedModel === model ? (
//                     <div className="absolute right-2 top-2 flex h-[14px] w-[14px] items-center justify-center rounded-full bg-primary">
//                       <Check size={10} className="text-card" />
//                     </div>
//                   ) : null}
//                   <img src={modelInfo.icon_url} alt={model} className="h-5 w-5 rounded-full" />
//                   <div className="text-sm font-medium text-foreground">{modelInfo.title}</div>
//                 </div>
//               );
//             })}
//           </div>

//           <ToolsSettings />

//           <OperationMode />

//           <DevOnly>
//             <>
//               <div className="mt-6 flex items-center gap-1 text-sm font-medium">
//                 Temperature
//                 <Bot size={14} />
//               </div>
//               <div className="text-xs text-caption-foreground">
//                 Controls randomness: lower values make responses more focused and deterministic,
//                 higher values make responses more creative and varied.
//               </div>
//               <div className="mt-5 flex w-[80%] items-end gap-2">
//                 <div className="text-xs">0</div>
//                 <Slider
//                   defaultValue={[temperature * 100]}
//                   max={100}
//                   step={10}
//                   className="mt-2"
//                   onValueChange={(value) => setTemperature(value[0] / 100)}
//                   thumbChildren={
//                     <div className="pointer-events-none absolute -top-5 text-xs">{temperature}</div>
//                   }
//                 />
//                 <div className="text-xs">1</div>
//               </div>
//             </>
//           </DevOnly>
//         </>
//       ) : null}
//     </div>
//   );
// };
