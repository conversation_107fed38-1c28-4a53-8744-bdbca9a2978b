// import { AssistantRunModeEnum } from '@repo/common/types/assistant/types';
// import { RadioGroup, RadioGroupItem } from '@repo/ui/components/ui/radio-group';
// import { useAtom } from 'jotai';
// import { Label } from '@/components/ui/label';

// import { operationModeAtom } from '../../hooks/useNewAssistant';

// export const OperationMode = () => {
//   const [operationMode, setOperationMode] = useAtom(operationModeAtom);

//   return (
//     <>
//       <div className="mt-6 text-sm font-medium">Run mode</div>
//       <div className="mb-3 text-xs text-caption-foreground">
//         Be aware: automatic mode may consume a large number of credits.
//       </div>
//       <RadioGroup
//         value={operationMode}
//         onValueChange={(value) => setOperationMode(value as AssistantRunModeEnum)}
//         className="flex flex-col gap-3"
//       >
//         <div className="flex items-center gap-4">
//           <RadioGroupItem value={AssistantRunModeEnum.MANUAL} id="r1" />
//           <Label htmlFor="r1" className="text-sm font-normal cursor-pointer">
//             Manual
//           </Label>
//         </div>
//         <div className="flex items-center gap-4">
//           <RadioGroupItem value={AssistantRunModeEnum.AUTOMATIC} id="r2" />
//           <Label htmlFor="r2" className="text-sm font-normal cursor-pointer">
//             Automatic
//           </Label>
//         </div>
//       </RadioGroup>
//     </>
//   );
// };
