// import { Textarea } from '@repo/ui/components/ui/textarea';
// import { useAtom } from 'jotai';

// import { descriptionAtom } from '../../hooks/useNewAssistant';

// export const Description = () => {
//   const [description, setDescription] = useAtom(descriptionAtom);

//   return (
//     <div className="flex flex-col">
//       <div className="text-sm font-medium text-foreground">Description</div>
//       <div className="mb-2 text-xs font-normal text-caption-foreground">
//         Describe what this assistant can do.
//       </div>
//       <Textarea
//         value={description}
//         onChange={(e) => setDescription(e.target.value)}
//         placeholder=""
//         className="h-[88px] rounded-[8px] border bg-transparent px-3 py-2 text-sm"
//       />
//     </div>
//   );
// };
