// import { Button } from '@repo/ui/components/ui/button';
// import { Dialog, DialogClose, DialogContent } from '@repo/ui/components/ui/dialog';
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
// } from '@repo/ui/components/ui/dropdown-menu';
// import { Textarea } from '@repo/ui/components/ui/textarea';
// import { useAtom } from 'jotai';
// import { X } from 'lucide-react';
// import { useCallback, useRef, useState } from 'react';
// import { userAtom } from '@/hooks/useUser';

// import { useNewAssistant } from '../../hooks/useNewAssistant';

// export const Instructions = () => {
//   const { instructions, setInstructions } = useNewAssistant();

//   // 全屏编辑状态
//   const [isFullScreen, setIsFullScreen] = useState(false);

//   // 为两个不同的文本区域创建单独的引用
//   const mainTextareaRef = useRef<HTMLTextAreaElement>(null);
//   const fullscreenTextareaRef = useRef<HTMLTextAreaElement>(null);
//   const [cursorPosition, setCursorPosition] = useState<number | null>(null);

//   const [user] = useAtom(userAtom);

//   // 可用的变量列表
//   const VARIABLES = [
//     { id: 'title', label: 'Title', value: '{{title}}' },
//     { id: 'content', label: 'Full content', value: '{{content}}' },
//     {
//       id: 'aiLanguage',
//       label: 'AI response language: ' + user?.preference?.ai_response_language,
//       value: '{{aiLanguage}}',
//     },
//     {
//       id: 'aiSecondLanguage',
//       label: 'AI secondary language: ' + user?.preference?.ai_2nd_response_language,
//       value: '{{aiSecondLanguage}}',
//     },
//   ];

//   // 获取当前活动的文本区域引用
//   const getActiveTextareaRef = useCallback(() => {
//     return isFullScreen ? fullscreenTextareaRef : mainTextareaRef;
//   }, [isFullScreen]);

//   // 更新光标位置的通用函数
//   const updateCursorPosition = useCallback(() => {
//     const activeTextarea = getActiveTextareaRef().current;
//     if (activeTextarea) {
//       setCursorPosition(activeTextarea.selectionStart);
//     }
//   }, [getActiveTextareaRef]);

//   // 关闭全屏模式
//   const closeFullScreen = useCallback(() => {
//     setIsFullScreen(false);
//     // 恢复光标位置需要在DOM更新后进行
//     setTimeout(() => {
//       if (cursorPosition !== null && mainTextareaRef.current) {
//         mainTextareaRef.current.focus();
//         mainTextareaRef.current.setSelectionRange(cursorPosition, cursorPosition);
//       }
//     }, 50); // 增加延迟时间，确保DOM已完全更新
//   }, [cursorPosition]);

//   // 插入变量的通用函数
//   const insertVariable = useCallback(
//     (variableValue: string) => {
//       const activeTextarea = getActiveTextareaRef().current;
//       if (activeTextarea) {
//         const start = activeTextarea.selectionStart;
//         const end = activeTextarea.selectionEnd;
//         const newText =
//           instructions.substring(0, start) + variableValue + instructions.substring(end);

//         setInstructions(newText);

//         // 计算新的光标位置
//         const newPosition = start + variableValue.length;

//         setTimeout(() => {
//           activeTextarea.focus();
//           activeTextarea.setSelectionRange(newPosition, newPosition);
//           setCursorPosition(newPosition);
//         }, 200);
//       }
//     },
//     [instructions, setInstructions, getActiveTextareaRef],
//   );

//   return (
//     <div>
//       <div className="text-sm font-medium text-foreground">Instructions</div>
//       <div className="text-xs font-normal text-caption-foreground">
//         Control your assistants behavior by adding custom instructions.
//       </div>

//       <div className="relative mt-2">
//         <div className="absolute right-0 top-[-30px] z-10 flex gap-2">
//           {/* <TooltipProvider>
//             <Tooltip>
//               <DropdownMenu>
//                 <DropdownMenuTrigger asChild>
//                   <TooltipTrigger asChild>
//                     <Button
//                       variant="ghost"
//                       size="icon"
//                       className="rounded-full h-7 w-7"
//                       onClick={(e) => {
//                         e.preventDefault();
//                         updateCursorPosition();
//                       }}
//                     >
//                       <PackagePlus className="w-4 h-4" />
//                     </Button>
//                   </TooltipTrigger>
//                 </DropdownMenuTrigger>
//                 <DropdownMenuContent>
//                   {VARIABLES.map((variable) => (
//                     <DropdownMenuItem
//                       key={variable.id}
//                       onClick={() => insertVariable(variable.value)}
//                     >
//                       {variable.label}
//                     </DropdownMenuItem>
//                   ))}
//                 </DropdownMenuContent>
//               </DropdownMenu>
//               <TooltipContent side="bottom" className="text-xs">
//                 Insert variable
//               </TooltipContent>
//             </Tooltip>
//           </TooltipProvider> */}

//           {/* <TooltipProvider>
//             <Tooltip>
//               <TooltipTrigger asChild>
//                 <Button
//                   variant="ghost"
//                   size="icon"
//                   className="rounded-full h-7 w-7"
//                   onClick={openFullScreen}
//                 >
//                   <Maximize2 className="w-4 h-4" />
//                 </Button>
//               </TooltipTrigger>
//               <TooltipContent side="bottom" className="text-xs">
//                 Full screen
//               </TooltipContent>
//             </Tooltip>
//           </TooltipProvider> */}
//         </div>

//         <Textarea
//           ref={mainTextareaRef}
//           className="max-h-[440px] min-h-[220px] resize-none overflow-auto rounded-[8px] border bg-transparent px-3 py-2 text-sm"
//           placeholder={`Tell Al what to do. Like "Summarize this into a short, shareable tweet for X or Instagram."`}
//           value={instructions}
//           onChange={(e) => {
//             setInstructions(e.target.value);
//             // 自动调整高度
//             e.target.style.height = 'auto';
//             e.target.style.height = `${Math.min(Math.max(e.target.scrollHeight, 220), 440)}px`;
//           }}
//           onFocus={updateCursorPosition}
//           onClick={updateCursorPosition}
//           onKeyUp={updateCursorPosition}
//           onSelect={updateCursorPosition}
//         />
//       </div>

//       {/* 全屏编辑对话框 */}
//       <Dialog open={isFullScreen} onOpenChange={(open) => !open && closeFullScreen()}>
//         <DialogContent showCloseButton={false} className="z-[9999] h-[85vh] max-w-[62vw] p-0">
//           <div className="flex flex-col h-full">
//             <div className="flex items-center justify-between p-4 border-b">
//               <div className="text-lg font-medium">Edit instructions</div>
//               <div className="flex items-center gap-2">
//                 <DropdownMenu>
//                   {/* <DropdownMenuTrigger asChild>
//                     <Button variant="ghost" size="sm" className="h-8 gap-1">
//                       <PackagePlus className="w-4 h-4" />
//                       <span>Insert variable</span>
//                     </Button>
//                   </DropdownMenuTrigger> */}
//                   <DropdownMenuContent align="end">
//                     {VARIABLES.map((variable) => (
//                       <DropdownMenuItem
//                         key={variable.id}
//                         onClick={() => insertVariable(variable.value)}
//                       >
//                         {variable.label}
//                       </DropdownMenuItem>
//                     ))}
//                   </DropdownMenuContent>
//                 </DropdownMenu>

//                 <DialogClose asChild>
//                   <Button variant="ghost" iconOnly onClick={closeFullScreen}>
//                     <X className="w-4 h-4" />
//                   </Button>
//                 </DialogClose>
//               </div>
//             </div>

//             <Textarea
//               ref={fullscreenTextareaRef}
//               className="flex-1 p-4 text-sm border-0 rounded-none resize-none"
//               value={instructions}
//               onChange={(e) => setInstructions(e.target.value)}
//               onFocus={updateCursorPosition}
//               onClick={updateCursorPosition}
//               onKeyUp={updateCursorPosition}
//               onSelect={updateCursorPosition}
//             />

//             <div className="flex justify-end p-4 border-t">
//               <Button onClick={closeFullScreen}>Done</Button>
//             </div>
//           </div>
//         </DialogContent>
//       </Dialog>
//     </div>
//   );
// };
