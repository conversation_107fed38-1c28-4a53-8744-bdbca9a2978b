// import * as RadioGroupPrimitive from '@radix-ui/react-radio-group';
// import { AssistantIconTypeEnum } from '@repo/common/types/assistant/types';
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
// } from '@repo/ui/components/ui/dropdown-menu';
// import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
// import { cn } from '@repo/ui/lib/utils';
// import { forwardRef, useEffect, useState } from 'react';

// import type { AssistantAvatarData } from '../../hooks/useNewAssistant';
// import { EMOJIs } from './emojis';
// import { ICONS, icons } from './icons';

// export const ICON_PALETTE = [
//   '--assistant-avatar-icon-black',
//   '--assistant-avatar-icon-red',
//   '--assistant-avatar-icon-brown',
//   '--assistant-avatar-icon-yellow',
//   '--assistant-avatar-icon-green',
//   '--assistant-avatar-icon-blue',
//   '--assistant-avatar-icon-purple',
//   '--assistant-avatar-icon-pink',
//   '--assistant-avatar-icon-gray',
// ];

// export const EMOJI_PALETTE = [
//   '--assistant-avatar-emoji-red',
//   '--assistant-avatar-emoji-brown',
//   '--assistant-avatar-emoji-yellow',
//   '--assistant-avatar-emoji-green',
//   '--assistant-avatar-emoji-blue',
//   '--assistant-avatar-emoji-purple',
//   '--assistant-avatar-emoji-pink',
//   '--assistant-avatar-emoji-gray',
// ];

// interface AssistantAvatorSelectorProps {
//   avatarData: AssistantAvatarData;
//   onChange: (avatarData: AssistantAvatarData) => void;
//   children: React.ReactNode;
// }

// export default function AssistantAvatorSelector({
//   avatarData,
//   children,
//   onChange,
// }: AssistantAvatorSelectorProps) {
//   const [open, setOpen] = useState(false);

//   const { trackButtonClick } = useTrackActions();

//   useEffect(() => {
//     randomAnAvatar(AssistantIconTypeEnum.BUILT_IN);
//   }, []);

//   const randomAnAvatar = (type: AssistantIconTypeEnum) => {
//     if (type === AssistantIconTypeEnum.EMOJI) {
//       onChange({
//         type: AssistantIconTypeEnum.EMOJI,
//         value: EMOJIs[Math.floor(Math.random() * EMOJIs.length)],
//         bg_color: EMOJI_PALETTE[Math.floor(Math.random() * EMOJI_PALETTE.length)],
//       });
//     }
//     if (type === AssistantIconTypeEnum.BUILT_IN) {
//       onChange({
//         type: AssistantIconTypeEnum.BUILT_IN,
//         value: ICONS[Math.floor(Math.random() * ICONS.length)],
//         bg_color: ICON_PALETTE[Math.floor(Math.random() * ICON_PALETTE.length)],
//       });
//     }
//   };

//   const isIcon = avatarData.type === AssistantIconTypeEnum.BUILT_IN;

//   return (
//     <DropdownMenu
//       open={open}
//       onOpenChange={(open) => {
//         setOpen(open);
//       }}
//     >
//       {children}
//       <DropdownMenuContent
//         side="bottom"
//         sideOffset={8}
//         align="end"
//         alignOffset={-24}
//         className="z-[1999] rounded-[16px] border-none"
//       >
//         {/* <RadioGroup
//           value={avatarData.bg_color}
//           onValueChange={(color) =>
//             onChange({ ...avatarData, bg_color: color })
//           }
//           className="grid w-full grid-cols-8 grid-rows-1 gap-4 px-2 mt-4"
//         >
//           {(isIcon ? ICON_PALETTE : EMOJI_PALETTE).map((name, index) => (
//             <RadioGroupItem
//               cssVariableName={name}
//               value={name}
//               key={index}
//             ></RadioGroupItem>
//           ))}
//         </RadioGroup> */}

//         {/* <Separator className="w-auto mx-3 my-4 bg-muted" /> */}

//         {/* <div className="flex items-center justify-between w-full pb-1 pr-1">
//           <div className="flex px-2">
//             <Button
//               variant="ghost"
//               className={cn(
//                 "h-5 px-2",
//                 isIcon ? "text-foreground" : "text-caption-foreground",
//               )}
//               onClick={() =>
//                 onChange({
//                   type: AssistantIconTypeEnum.BUILT_IN,
//                   value: ICONS[0],
//                   bg_color: ICON_PALETTE[2],
//                 })
//               }
//             >
//               Icon
//             </Button>
//             <Button
//               variant="ghost"
//               className={cn(
//                 "h-5 px-2",
//                 !isIcon ? "text-foreground" : "text-caption-foreground",
//               )}
//               onClick={() =>
//                 onChange({
//                   type: AssistantIconTypeEnum.EMOJI,
//                   value: EMOJIs[0],
//                   bg_color: EMOJI_PALETTE[2],
//                 })
//               }
//             >
//               Emoji
//             </Button>
//           </div>
//           <Button
//             variant="ghost"
//             className={cn("h-6 w-6 p-0")}
//             onClick={() => randomAnAvatar(avatarData.type)}
//           >
//             <RefreshCcw size={16} />
//           </Button>
//         </div> */}

//         <div className="grid grid-cols-6 gap-0 p-0 overflow-x-hidden overflow-y-auto">
//           {(isIcon ? ICONS : EMOJIs).map((value, i) => {
//             // @ts-expect-error icon value is dynamic
//             const CurrentIcon = icons[value] || icons.Planet;
//             return (
//               <DropdownMenuItem
//                 key={i}
//                 className={cn(
//                   'm-[3px] flex h-8 w-8 cursor-pointer items-center justify-center rounded-[10px] border-accent !p-0',
//                   avatarData.value === value && 'border bg-accent',
//                 )}
//                 data-value={value}
//                 onClick={(e) => {
//                   e.preventDefault();
//                   onChange({
//                     ...avatarData,
//                     value: value,
//                   });
//                   // 上报埋点
//                   trackButtonClick('assistant_avatar_select', {
//                     avatar_type: avatarData.type,
//                     avatar_value: value,
//                   });
//                 }}
//               >
//                 {isIcon ? <CurrentIcon size={24} /> : value}
//               </DropdownMenuItem>
//             );
//           })}
//         </div>
//       </DropdownMenuContent>
//     </DropdownMenu>
//   );
// }

// const RadioGroupItem = forwardRef<
//   React.ElementRef<typeof RadioGroupPrimitive.Item>,
//   React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item> & {
//     cssVariableName: string;
//   }
// >(({ className, cssVariableName, ...props }, ref) => {
//   const color = `hsl(var(${cssVariableName}))`;
//   return (
//     <RadioGroupPrimitive.Item
//       ref={ref}
//       className={cn('relative aspect-square h-5 w-5 rounded-full', className)}
//       style={{ color }}
//       {...props}
//     >
//       <div
//         className="w-full h-full rounded-full"
//         style={{
//           backgroundColor: color,
//         }}
//       ></div>
//       <div className="absolute top-0 left-0 w-full h-full bg-transparent border rounded-full border-divider"></div>
//       <RadioGroupPrimitive.Indicator
//         className="absolute left-[-4px] top-[-4px] flex h-[28px] w-[28px] items-center justify-center rounded-full border-[2px]"
//         style={{
//           borderColor: color,
//         }}
//       ></RadioGroupPrimitive.Indicator>
//     </RadioGroupPrimitive.Item>
//   );
// });
// RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName;
