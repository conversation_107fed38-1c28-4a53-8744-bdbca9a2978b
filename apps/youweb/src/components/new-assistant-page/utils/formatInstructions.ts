// import { type AllLanguageEnumKeys, iso6391ToLanguage } from '@repo/common/types';
// import { BoardItemTypeEnum } from '@repo/common/types/board-item/types';
// import { removeMarkdownLinks } from '@youmindinc/youcommon';
// import type { BoardTreeItem } from '@/typings/board-item';
// import { callHTTP } from '@/utils/callHTTP';
// import { getLanguageEnum } from '@/utils/language';

// interface Params {
//   instructions: string;
//   selectedItem: BoardTreeItem | null;
//   ai_response_language: string;
//   ai_2nd_response_language: string;
// }

// export const formatInstructions = async (params: Params) => {
//   const { instructions, selectedItem, ai_response_language, ai_2nd_response_language } = params;

//   // console.log("selectedItem", selectedItem);

//   if (!selectedItem) {
//     return instructions;
//   }

//   // eslint-disable-next-line @typescript-eslint/no-explicit-any
//   let entity: any = selectedItem.entity;

//   if (selectedItem.entity_type === BoardItemTypeEnum.SNIP) {
//     // 请求到完整的 Entity
//     const { data } = await callHTTP('/api/v1/snip/getSnip', {
//       method: 'POST',
//       body: {
//         id: selectedItem.entity.id,
//       },
//     });
//     entity = data;
//   }

//   // console.log("entity", entity);

//   let formattedInstructions = instructions;

//   let content = '';
//   if (instructions.includes('{{content}}')) {
//     const plainContent = entity?.content?.plain;
//     // Chat
//     if (selectedItem?.entity_type === BoardItemTypeEnum.CHAT) {
//       // @ts-expect-error 不管不管
//       const messagesContent = entity?.messages?.map((message) => {
//         return `
//           ${message.role}: ${message.content}
//         `;
//       });
//       if (messagesContent) {
//         content = messagesContent.join('\n');
//       }
//     }
//     // Thought
//     if (selectedItem?.entity_type === BoardItemTypeEnum.THOUGHT) {
//       if (plainContent) {
//         content = plainContent;
//       }
//     }
//     // Snip
//     if (selectedItem?.entity_type === BoardItemTypeEnum.SNIP) {
//       const snipType = entity?.type;
//       if (snipType === 'article') {
//         if (plainContent) {
//           content = plainContent;
//         }
//       }
//       if (snipType === 'image') {
//         const descContent = entity?.description;
//         if (descContent) {
//           content = descContent;
//         }
//       }
//       if (snipType === 'video' || snipType === 'audio') {
//         const transcriptContent = entity?.transcript?.contents?.[0]?.plain;
//         if (transcriptContent) {
//           content = transcriptContent;
//         }
//       }
//     }

//     // console.log("content", content);

//     if (content) {
//       formattedInstructions = formattedInstructions.replaceAll('{{content}}', content);
//     } else {
//       formattedInstructions = formattedInstructions.replaceAll(
//         '{{content}}',
//         plainContent || ' No content ',
//       );
//     }
//   }

//   // 计算语言
//   let aiLanguage = ai_response_language as AllLanguageEnumKeys;
//   let aiSecondLanguage = ai_2nd_response_language as AllLanguageEnumKeys;

//   if (aiLanguage === 'system') {
//     aiLanguage = iso6391ToLanguage[navigator.language] || 'en-US';
//   }
//   if (aiSecondLanguage === 'system') {
//     aiSecondLanguage = iso6391ToLanguage[navigator.language] || 'en-US';
//   }

//   if (aiLanguage === 'follow-content') {
//     aiLanguage =
//       getLanguageEnum(removeMarkdownLinks(content || formattedInstructions || '')) || 'en-US';
//   }

//   if (aiSecondLanguage === 'follow-content') {
//     aiSecondLanguage =
//       getLanguageEnum(removeMarkdownLinks(content || formattedInstructions || '')) || 'en-US';
//   }

//   formattedInstructions = formattedInstructions.replaceAll('{{aiLanguage}}', aiLanguage);
//   formattedInstructions = formattedInstructions.replaceAll(
//     '{{aiSecondLanguage}}',
//     aiSecondLanguage,
//   );

//   // 标题
//   const title = entity?.title;
//   if (title) {
//     formattedInstructions = formattedInstructions.replaceAll('{{title}}', title);
//   }

//   return formattedInstructions;
// };
