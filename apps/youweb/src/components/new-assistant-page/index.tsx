// import { useAtom } from 'jotai';
// import { ChevronLeft } from 'lucide-react';

// import { cn } from '@/utils/utils';

// import { Button } from '../ui/button';
// import { Advanced } from './components/Advanced';
// import { Instructions } from './components/InstructionsComponent';
// import { NameAndAvatar } from './components/NameAndAvatar';
// import { newAssistantPageOpenAtom } from './hooks/useNewAssistant';
// import styles from './index.module.css';

// export const NewAssistantPage = () => {
//   const [, setNewAssistantPageOpen] = useAtom(newAssistantPageOpenAtom);

//   return (
//     <div
//       // z-index 需要小于 antd image 的 1000
//       className={cn(styles.slideIn, 'absolute inset-0 z-[999] flex bg-card')}
//     >
//       {/* 左半边 */}
//       <div className="w-[50%] flex-shrink-0 overflow-y-auto px-[24px] py-[26px]">
//         <div className="flex items-center gap-1">
//           <Button
//             className="h-7 w-7 rounded-full"
//             variant="ghost"
//             size="icon"
//             onClick={() => {
//               setNewAssistantPageOpen(false);
//             }}
//           >
//             <ChevronLeft size={16} />
//           </Button>
//           <div className="text-sm font-medium">New assistant</div>
//         </div>

//         <div className="mt-8 px-[4vw]">
//           <NameAndAvatar />
//         </div>

//         {/* <div className="mt-6 px-[4vw]">
//           <Description />
//         </div> */}

//         <div className="mt-6 px-[4vw]">
//           <Instructions />
//         </div>

//         <div className="mt-6 px-[4vw]">
//           <Advanced />
//         </div>
//       </div>

//       <div className="h-full w-[50%] flex-shrink-0 p-3">{/* <Preview /> */}</div>
//     </div>
//   );
// };
