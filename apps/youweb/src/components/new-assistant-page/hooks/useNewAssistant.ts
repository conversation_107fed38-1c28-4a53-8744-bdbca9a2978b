// import { TOOL_TYPES } from '@repo/common/consts/tool/const';
// import {
//   type Assistant,
//   AssistantIconTypeEnum,
//   AssistantRunModeEnum,
//   type PromptExecuterAssistant,
// } from '@repo/common/types/assistant/types';
// import { ASK_AI_MODELS, DEFAULT_AI_CHAT_MODEL, LLMs } from '@repo/common/types/chat/enum';
// import type { CompletionBlock } from '@repo/common/types/chat/types';
// import { toast } from '@repo/ui/components/ui/sonner';
// import { atom, useAtom, useSetAtom } from 'jotai';
// import { useEffect, useMemo, useRef } from 'react';
// import { createEmptyPureChatAtom } from '@/hooks/scoped/base-chat/useBaseChatInitialization';
// import { handleReceiveCompletionChunkAtom } from '@/hooks/scoped/base-chat/useSendBaseChatMessage';
// import { boardDetailAtom } from '@/hooks/useBoards';
// import { addToolAndSetActiveAtom, updateToolAndSetActiveAtom } from '@/hooks/useMindStudio';
// import type { BoardTreeItem } from '@/typings/board-item';
// import { callHTTP, callHTTPStream } from '@/utils/callHTTP';
// import { ICONS } from '../components/AssistantAvatorSelector/icons';

import { AssistantIconTypeEnum } from '@repo/common/types/assistant';

// // 从 LLMs 中选择特定的几个模型作为 SupporttedModel
// export type SupporttedModel = (typeof ASK_AI_MODELS)[number];

export type AssistantAvatarData = {
  type: AssistantIconTypeEnum;
  value: string;
  bg_color: string;
};

// // 是否打开 board 的 new-assistant-page
// export const newAssistantPageOpenAtom = atom(false);

// export const nameAtom = atom('');
// export const descriptionAtom = atom('');
// export const avatarDataAtom = atom<AssistantAvatarData>({
//   type: AssistantIconTypeEnum.BUILT_IN,
//   value: ICONS[Math.floor(Math.random() * ICONS.length)],
//   bg_color: '--assistant-avatar-icon-yellow',
// });
// export const instructionsAtom = atom('');
// export const previewValueAtom = atom('');
// export const previewBlocksAtom = atom<CompletionBlock[]>([]);
// export const generatingAtom = atom(false);
// export const selectedItemAtom = atom<BoardTreeItem | null>(null);
// export const selectedModelAtom = atom<SupporttedModel>(LLMs.GEMINI_25_FLASH);
// export const temperatureAtom = atom(0.5);
// export const adaptorAtom = atom<'markdown' | 'overview' | 'plain-text' | 'mindmap'>('markdown');
// export const operationModeAtom = atom<AssistantRunModeEnum>(AssistantRunModeEnum.MANUAL);

// const INITIAL_TOOLS = {
//   [TOOL_TYPES.GOOGLE_SEARCH]: false,
//   [TOOL_TYPES.IMAGE_GENERATE]: false,
//   [TOOL_TYPES.DIAGRAM_GENERATE]: false,
// };
// export const toolsAtom = atom<Partial<Record<TOOL_TYPES, boolean>>>(INITIAL_TOOLS);

// // 初始的 Assistant，用于编辑
// export const initialAssistantAtom = atom<PromptExecuterAssistant | null>(null);

// export const useNewAssistant = () => {
//   const [instructions, setInstructions] = useAtom(instructionsAtom);
//   const [generating, setGenerating] = useAtom(generatingAtom);
//   const [selectedItem, setSelectedItem] = useAtom(selectedItemAtom);
//   const [selectedModel, setSelectedModel] = useAtom(selectedModelAtom);
//   const [temperature, setTemperature] = useAtom(temperatureAtom);
//   const [adaptor, setAdaptor] = useAtom(adaptorAtom);
//   const [avatarData, setAvatarData] = useAtom(avatarDataAtom);
//   const [name, setName] = useAtom(nameAtom);
//   const [description, setDescription] = useAtom(descriptionAtom);
//   const abortControllerRef = useRef<AbortController | null>(null);
//   const [tools, setTools] = useAtom(toolsAtom);
//   const [operationMode, setOperationMode] = useAtom(operationModeAtom);

//   const [, setNewAssistantPageOpen] = useAtom(newAssistantPageOpenAtom);

//   const [, addToolAndSetActive] = useAtom(addToolAndSetActiveAtom);
//   const [, updateToolAndSetActive] = useAtom(updateToolAndSetActiveAtom);

//   const handleReceiveCompletionChunk = useSetAtom(handleReceiveCompletionChunkAtom);

//   const [board] = useAtom(boardDetailAtom);

//   const [initialAssistant, setInitialAssistantAtom] = useAtom(initialAssistantAtom);

//   const createEmptyPureChat = useSetAtom(createEmptyPureChatAtom);

//   useEffect(() => {
//     if (!initialAssistant) {
//       return;
//     }

//     setName(initialAssistant.name);
//     setDescription(initialAssistant.description);
//     if (initialAssistant.icon) {
//       setAvatarData(initialAssistant.icon);
//     }
//     setInstructions(initialAssistant.instructions || '');
//     setTools(initialAssistant.tools || INITIAL_TOOLS);
//     setOperationMode(initialAssistant.run_mode || AssistantRunModeEnum.MANUAL);
//     if (
//       !initialAssistant.model ||
//       !ASK_AI_MODELS.includes(initialAssistant.model as SupporttedModel)
//     ) {
//       initialAssistant.model = DEFAULT_AI_CHAT_MODEL;
//     }
//     setSelectedModel(initialAssistant.model as SupporttedModel);
//     setTemperature(initialAssistant.temperature || 0.5);
//   }, [initialAssistant]);

//   const triggerRun = async () => {
//     if (generating) {
//       return;
//     }
//     // 如果没选择一个 item，则提示用户选择一个 item
//     if (!selectedItem) {
//       toast('Please select an item');
//       return;
//     }

//     // 如果存在之前的abortController，先中止它
//     if (abortControllerRef.current) {
//       abortControllerRef.current.abort();
//     }

//     // 创建新的AbortController
//     abortControllerRef.current = new AbortController();

//     setGenerating(true);

//     // 设置 chatAtom
//     createEmptyPureChat();

//     try {
//       const response = await callHTTPStream(`/api/v2/assistant/previewAssistant`, {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: {
//           instructions,
//           source: {
//             entity_type: selectedItem?.entity_type || 'board',
//             entity_id: selectedItem?.entity.id || board?.id,
//           },
//           model: selectedModel,
//           temperature,
//           tools,
//         },
//         signal: abortControllerRef.current.signal,
//         onMessage: (chunk: any) => handleReceiveCompletionChunk(chunk),
//       });

//       if (response.error) {
//         throw new Error(`HTTP error! status: ${response.error.status}`);
//       }
//     } catch (error) {
//       if (error instanceof Error && error.name !== 'AbortError') {
//         console.error('Generate an abstract error:', error);
//       }
//     } finally {
//       setGenerating(false);
//     }
//   };

//   const abort = () => {
//     if (abortControllerRef.current) {
//       abortControllerRef.current.abort();
//     }
//   };

//   const canBeDone = useMemo(() => {
//     return name && instructions && avatarData;
//   }, [name, instructions, avatarData]);

//   const cleanForm = () => {
//     setName('');
//     setDescription('');
//     setInstructions('');
//     setInitialAssistantAtom(null);
//   };

//   const handleNewAssistantDone = async () => {
//     if (!canBeDone) {
//       return;
//     }

//     const params: Partial<Assistant> = {
//       name,
//       description,
//       instructions,
//       model: selectedModel,
//       temperature,
//       icon: avatarData,
//       tools,
//       run_mode: operationMode,
//     };
//     const isCreate = !initialAssistant;
//     let request = () =>
//       callHTTP('/api/v1/assistant/createAssistant', {
//         method: 'POST',
//         body: params,
//       });
//     if (!isCreate) {
//       params.id = initialAssistant.id;
//       request = () =>
//         callHTTP(`/api/v1/assistant/patchAssistant`, {
//           method: 'POST',
//           body: params,
//         });
//     }
//     const res = await request();
//     if (res.error) {
//       toast('Something went wrong.');
//     } else {
//       setNewAssistantPageOpen(false);
//       // 清空一下咱们表单
//       cleanForm();
//       // 更新一下 tools atom
//       if (isCreate) {
//         addToolAndSetActive(res.data);
//       } else {
//         // 更新工具
//         updateToolAndSetActive(initialAssistant.id, res.data);
//       }
//       // 设置 active
//     }
//   };

//   return {
//     name,
//     cleanForm,
//     instructions,
//     setInstructions,
//     triggerRun,
//     abort,
//     generating,
//     selectedItem,
//     setSelectedItem,
//     selectedModel,
//     setSelectedModel,
//     temperature,
//     setTemperature,
//     adaptor,
//     setAdaptor,
//     handleNewAssistantDone,
//     canBeDone,
//   };
// };
