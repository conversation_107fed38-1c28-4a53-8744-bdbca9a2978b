import { Button } from '@repo/ui/components/ui/button';
import { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useIsIOS } from '@/hooks/useIsIOS';
import { useIsMobile } from '@/hooks/useIsMobile';
import { useIsWeChat } from '@/hooks/useIsWeChat';
import { BlankPageBackground } from './BlankPageBackground';
import { YouMindHead } from './YouMindHead';

const appStoreUrl = 'https://apps.apple.com/app/6744521608';

// Mobile unavailable overlay component
export function MobileUnavailableOverlay() {
  const location = useLocation();
  // 从路径中解析出路径段，类似于 useSelectedLayoutSegments 的功能
  const segments = location.pathname.split('/').filter(Boolean);

  const { isMobile } = useIsMobile();
  const { isIOS } = useIsIOS();
  const { isWeChat } = useIsWeChat();
  const [showMobileOverlay, setShowMobileOverlay] = useState(false);

  useEffect(() => {
    // snip detail 页跳过检测
    if (segments?.[1] === 'snips' && segments?.[2]) {
      return;
    }

    // Mobile 应用路由跳过检测
    if (segments?.[0] === 'mobileThought' || segments?.[0] === 'mobileEditor') {
      return;
    }

    // /mobile/* 路由跳过检测
    if (segments?.[0] === 'mobile') {
      return;
    }

    if (isMobile) {
      setShowMobileOverlay(true);
    } else {
      setShowMobileOverlay(false);
    }
  }, [isMobile, segments]);

  const handleAppStoreClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (isWeChat) {
      alert('Please open this page in the system browser.');
      return;
    }
    if (isIOS && appStoreUrl) {
      window.location.href = appStoreUrl;
    }
  };

  if (!showMobileOverlay) {
    return null;
  }

  const showAppStore = isIOS && appStoreUrl;

  return (
    <div className="fixed inset-0 z-[999999999] flex h-full w-full items-center justify-center bg-background">
      <div className="fixed top-0 z-40 h-[80px] w-full">
        <YouMindHead />
      </div>
      <div className="overflow-hidden">
        <div className="absolute top-0 left-0 z-0">
          <BlankPageBackground />
        </div>
        <img
          alt=""
          className="mx-auto w-[200px]"
          src="https://cdn.gooo.ai/assets/no-content-in-unavailable.png"
        />

        <div className="mb-[4vh] mt-[4vh] px-5 text-center text-base text-muted-foreground">
          {showAppStore ? (
            <>
              <p>For better experience,</p>
              <p>please download our app.</p>
            </>
          ) : (
            <>
              <p>Mobile version is currently unavailable.</p>
              <p>Please visit on a desktop computer.</p>
            </>
          )}
        </div>
        {showAppStore && (
          <a href={appStoreUrl!} className="relative z-10" onClick={handleAppStoreClick}>
            <div className="mt-5 text-base text-center">
              <Button
                className="w-[224px]"
                // className="w-[224px] rounded-full px-5"
              >
                <img
                  src="/login/apple-dark.svg"
                  width={18}
                  height={18}
                  alt="App Store"
                  className="mr-2"
                />
                App Store
              </Button>
            </div>
          </a>
        )}
        <Link to="/overview" className="relative z-10">
          <div className="mt-5 text-base text-center">
            <Button
              variant="outline"
              // className="w-[224px] rounded-full px-5"
            >
              Go to homepage
            </Button>
          </div>
        </Link>
      </div>
    </div>
  );
}
