import { type BoardItem, BoardItemTypeEnum } from '@repo/common/types/board-item/types';
import type { ChatDetail } from '@repo/common/types/chat/types';
import type { SnipVO } from '@repo/common/types/snip/app-types';
import type { SnipTypeEnum } from '@repo/common/types/snip/types';
import type { ThoughtVO } from '@repo/common/types/thought/types';
import { Button } from '@repo/ui/components/ui/button';
import { DialogTrigger } from '@repo/ui/components/ui/dialog';
import { useAtom } from 'jotai';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';
import { NewSnip } from '@/components/icon/new-snip';
import { NewThought } from '@/components/icon/new-thought';
import { TypeFilter } from '@/components/search/components/TypeFilter';
import { SnipCreatorDialog } from '@/components/snip/snip-creator-dialog';
import { createBoardThoughtAtom } from '@/hooks/useBoardState';
import { addBoardItemsAndOpenAtom } from '@/hooks/useBoards';

interface MasonryToolbarProps {
  groupId?: string;
  groupItemId?: string;
  boardId: string;
  selectedTypes: SnipTypeEnum[];
  onChange: (types: SnipTypeEnum[]) => void;
}

export const MasonryToolbar = ({
  groupId,
  groupItemId,
  boardId,
  selectedTypes,
  onChange,
}: MasonryToolbarProps) => {
  const [isThoughtPending, setIsThoughtPending] = useState(false);
  const [, addBoardItemsAndOpen] = useAtom(addBoardItemsAndOpenAtom);
  const [, createBoardThought] = useAtom(createBoardThoughtAtom);

  const afterMaterialCreated = (
    type: BoardItemTypeEnum,
    items: SnipVO[] | ThoughtVO[] | (ChatDetail & { board_item: BoardItem })[],
  ) => {
    if (items.length) {
      const boardItems = items.map((item) => ({
        ...item.board_item!,
        parentId: groupItemId,
        parent_board_group_id: groupId,
        entity: item,
        entity_type: type,
      }));
      addBoardItemsAndOpen(boardItems);
    }
  };

  return (
    <div className="mb-2 flex h-8 w-full items-center justify-between px-3">
      <div className="flex">
        <div className="mr-4">
          <Button
            disabled={isThoughtPending}
            variant="secondary"
            onClick={async () => {
              setIsThoughtPending(true);
              try {
                const thought = await createBoardThought(boardId, groupId);
                if (thought) {
                  afterMaterialCreated(BoardItemTypeEnum.THOUGHT, [thought]);
                }
              } catch (error) {
                console.error('Failed to create thought:', error);
              } finally {
                setIsThoughtPending(false);
              }
            }}
          >
            {isThoughtPending ? (
              <Loader2 size={16} className="animate-spin" />
            ) : (
              <NewThought size={18} />
            )}
            {'Thought'}
          </Button>
        </div>
        <SnipCreatorDialog boardId={boardId} boardGroupId={groupId} autoChangePanelData>
          <DialogTrigger asChild>
            <Button variant="secondary" disabled={isThoughtPending}>
              <NewSnip size={18} />
              {'Snip'}
            </Button>
          </DialogTrigger>
        </SnipCreatorDialog>
      </div>
      <TypeFilter
        className="h-8 rounded-full"
        selectedTypes={selectedTypes}
        onChange={onChange}
        disabled={isThoughtPending}
        source="board"
      />
    </div>
  );
};
