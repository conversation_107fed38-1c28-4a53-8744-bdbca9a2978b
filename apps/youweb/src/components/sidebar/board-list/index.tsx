'use client';

import { BoardStatusEnum } from '@repo/common/types/board/types';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import { AnimatePresence, motion } from 'framer-motion';
import { useAtom, useAtomValue } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
import { ChevronDown, ChevronRight, Ellipsis } from 'lucide-react';
import { Link } from 'react-router-dom';
import { boardsAtom } from '@/hooks/useBoards';
import { useTranslation } from '@/hooks/useTranslation';
import { cn } from '@/utils/utils';

import { BoardItem } from './board-item';

const MAX_BOARD_COUNT = 10;

interface BoardListProps {
  className?: string;
}

const collapseBoardListAtom = atomWithStorage('collapseBoardList', false);

export function BoardList({ className }: BoardListProps) {
  const { t } = useTranslation('Sidebar');
  const { trackButtonClick } = useTrackActions();
  const boards = useAtomValue(boardsAtom);
  const [collapseBoardList, setCollapseBoardList] = useAtom(collapseBoardListAtom);

  if (boards.length === 0) {
    return null;
  }

  return (
    <ul className={cn('flex flex-col items-start text-foreground', className)}>
      <li
        className="flex flex-row items-center justify-between w-full px-2 text-xs rounded-sm cursor-pointer group h-7 text-caption hover:bg-card-snips"
        onClick={() => setCollapseBoardList(!collapseBoardList)}
      >
        <span>{t('boards')} </span>
        <span className="hidden group-hover:inline">
          {collapseBoardList ? <ChevronRight size={12} /> : <ChevronDown size={12} />}
        </span>
      </li>
      <AnimatePresence initial={false}>
        {!collapseBoardList && (
          <motion.div
            initial={{ opacity: 0, height: 0, overflow: 'hidden' }}
            animate={{ opacity: 1, height: 'auto', overflow: 'visible' }}
            exit={{ opacity: 0, height: 0, overflow: 'hidden' }}
            transition={{ duration: 0.2, ease: 'easeInOut' }}
            className="w-full"
          >
            {boards
              .filter((item) => item.status !== BoardStatusEnum.OTHER)
              .slice(0, MAX_BOARD_COUNT)
              .map((item, index) => {
                return <BoardItem key={index} board={item} />;
              })}
            {boards.length >= MAX_BOARD_COUNT && (
              <li className={cn('w-full')}>
                <Link
                  className={cn(
                    'my-0.5 flex flex-1 cursor-pointer flex-row items-center rounded-md px-2 text-sm text-caption transition-colors hover:bg-card-snips hover:text-card-foreground',
                  )}
                  to={`/boards`}
                  onClick={() => {
                    trackButtonClick('sidebar_board_more_click');
                  }}
                >
                  <div className="flex flex-row items-center flex-1 w-full h-8">
                    <Ellipsis size={18} />

                    <div className="ml-2">{'More'}</div>
                  </div>
                </Link>
              </li>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </ul>
  );
}
