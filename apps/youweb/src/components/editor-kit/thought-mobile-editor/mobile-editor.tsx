import { createDocByBase64, createDocByJSONContent } from '@repo/editor-common';
import type { EditorProps } from '@tiptap/pm/view';
import { type Editor, EditorContent, type JSONContent, useEditor } from '@tiptap/react';
import cn from 'clsx';
import { forwardRef, memo, useState } from 'react';
import { IndexeddbPersistence } from 'y-indexeddb';
import type { Doc } from 'yjs';
import {
  getMobileEditorExtension,
  type MobileEditorExtensionOptions,
} from './mobile-editor-extension';

export interface EditorOnReadyParams {
  editor: Editor;
  ydoc: Doc;
  indexDB?: IndexeddbPersistence | null;
}

export interface MobileEditorProps {
  id?: string;
  editorProps?: EditorProps;
  onCreate?: (params: EditorOnReadyParams) => void;
  content?: string | JSONContent;
  editorClassName?: string;
  storeOptions?: {
    indexeddbStoreEnable?: boolean;
  };
  extensionsOptions: MobileEditorExtensionOptions['extensionsOptions'];
}

export type MobileEditorRef = {};

export const MobileEditor = memo(
  forwardRef<MobileEditorRef, MobileEditorProps>(
    ({ id, content, onCreate, editorClassName, storeOptions, extensionsOptions }, _ref) => {
      const [indexDB, setIndexDB] = useState<IndexeddbPersistence | null>(null);
      const [ydoc] = useState<Doc>(() => {
        const ydoc =
          typeof content === 'string' || !content
            ? createDocByBase64(content ?? '')
            : createDocByJSONContent(content ?? {});
        if (storeOptions?.indexeddbStoreEnable && id) {
          setIndexDB(new IndexeddbPersistence(id, ydoc));
        }
        return ydoc;
      });

      console.log(content, 'dongdong look here');

      const editor = useEditor(
        {
          extensions: getMobileEditorExtension({
            ydoc,
            extensionsOptions,
          }),
          immediatelyRender: true,
          shouldRerenderOnTransaction: false,
          editorProps: {
            attributes: {
              spellcheck: 'false',
            },
          },
          onCreate: ({ editor }) => {
            onCreate?.({ editor, ydoc, indexDB });
          },
        },
        [ydoc],
      );

      return (
        <div className={cn('mobile-editor-container', editorClassName)}>
          <EditorContent editor={editor} />
        </div>
      );
    },
  ),
  (prevProps, nextProps) => {
    return prevProps.id === nextProps.id;
  },
);

MobileEditor.displayName = 'MobileEditor';
