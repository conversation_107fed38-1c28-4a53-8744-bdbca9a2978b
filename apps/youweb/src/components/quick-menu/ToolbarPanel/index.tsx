import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@repo/ui/components/ui/tooltip';
import { useTrackActions } from '@repo/ui/lib/posthog/useTrackActions';
import clsx from 'clsx';
import { useAtomValue, useSetAtom } from 'jotai';
import { Check, Copy, Loader2 } from 'lucide-react';
import { useLayoutEffect, useRef, useState } from 'react';
import { AskAIBrandIcon } from '@/components/icon/ask-ai';
import { askAISelectionAtom } from '@/hooks/ask-ai/useChatPanelState';
import { boardDetailAtom } from '@/hooks/useBoards';
import { openMindStudioIfNotActiveAtom, setChatPanelActiveAtom } from '@/hooks/useMindStudio';
import { handleCopyAElement, handleCopyImageByCanvas } from '@/utils/copy';
import { generateId } from '@/utils/generateId';
// import { QuickSnipButton } from "./QuickSnipButton";
import { PanelState } from '../QuickMenu';
import styles from './index.module.css';
import { QuickHighlightButton } from './QuickHighlightButton';

interface CommonProps {
  changePanelState: (params: PanelState) => void;
  triggerHide?: boolean;
  lockStatus?: 'click' | 'drag' | null;
  closeMenu: () => void;
}

interface SelectProps extends CommonProps {
  type: 'select';
  imageOnly?: boolean;
}

interface CaptureProps extends CommonProps {
  type: 'capture';
}

export type ToolbarPanelProps = SelectProps | CaptureProps;

export const ToolbarPanel: React.FC<ToolbarPanelProps> = (props) => {
  const { changePanelState, type, triggerHide, lockStatus, closeMenu } = props;

  const openMindStudioIfNotActive = useSetAtom(openMindStudioIfNotActiveAtom);
  const boardDetail = useAtomValue(boardDetailAtom);

  const setAskAISelection = useSetAtom(askAISelectionAtom);
  const setChatPanelActive = useSetAtom(setChatPanelActiveAtom);

  const [copySuccess, setCopySuccess] = useState(false);
  const [copyLoading, setCopyLoading] = useState(false);

  const { trackButtonClick } = useTrackActions();

  const keyRef = useRef<string>('');

  useLayoutEffect(() => {
    keyRef.current = generateId();
  }, []);

  const handleCopy = async () => {
    if (type === 'select') {
      // 如果是图片
      if (props.imageOnly) {
        setCopyLoading(true);
        // 获取当前 selection 的图片元素
        const selection = window.getSelection();
        if (!selection?.rangeCount) return;
        const range = selection.getRangeAt(0);
        const container = range.commonAncestorContainer;
        const element =
          container.nodeType === Node.ELEMENT_NODE
            ? (container as Element)
            : container.parentElement;

        if (!element) return;

        const imageElement =
          element.tagName === 'IMG' || element.tagName === 'PICTURE'
            ? element
            : element.querySelector('img, picture');

        if (!imageElement) return;

        const imageSrc = getImageSrc(imageElement);
        if (!imageSrc) return;

        const success = await handleCopyImageByCanvas(imageSrc);
        if (success) {
          setCopySuccess(true);
          setTimeout(() => {
            setCopySuccess(false);
          }, 1000);
        }
        setCopyLoading(false);
        return;
      }
      // 文本内容
      const selection = window.getSelection();
      if (!selection?.rangeCount) return;
      const container = document.createElement('div');
      for (let i = 0; i < selection.rangeCount; i++) {
        container.appendChild(selection.getRangeAt(i).cloneContents());
      }
      await handleCopyAElement(container);
    }
    changePanelState({
      mode: 'notice',
      noticeChildren: (
        <>
          <Check size={14} />
          <span style={{ marginRight: 12, fontSize: 12 }}>Copied</span>
        </>
      ),
    });
  };

  // 将当前划线的内容塞给面板
  const handleAskAI = async () => {
    if (!boardDetail?.id) {
      console.warn('board is not found');
      return;
    }
    // 获取当前划线的文本。图片转换为 alt
    const selection = window.getSelection();
    if (!selection?.rangeCount) return;
    const sourceContent = selection.toString().trim();
    setAskAISelection(sourceContent);
    // 如果 Mind Studio 没开启，就打开
    await openMindStudioIfNotActive(boardDetail?.id, 100);
    // Focus Chat
    setTimeout(() => {
      // 设置面板为 active
      setChatPanelActive();
      setTimeout(() => {
        window.dispatchEvent(new Event('youmind-focus-chat-editor'));
      });
    });
  };

  const renderHighlight = () => {
    if (type === 'capture') {
      return null;
    }
    if (type === 'select' && props.imageOnly) {
      return null;
    }
    return <QuickHighlightButton toolbarKey={keyRef.current} />;
  };

  const renderCopyIcon = () => {
    if (copyLoading) {
      return <Loader2 size={16} className="animate-spin" />;
    }
    return copySuccess ? <Check size={16} /> : <Copy size={16} />;
  };

  const renderAskAI = () => {
    if (type !== 'select') {
      return null;
    }
    if (props.imageOnly) {
      return null;
    }
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={styles.item}
              onClick={() => {
                handleAskAI();
                // 上报埋点
                trackButtonClick('toolbar_ask_ai_click');
              }}
            >
              <AskAIBrandIcon size={16} />
            </div>
          </TooltipTrigger>
          <TooltipContent side="top" className="text-xs">
            {`Ask AI`}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  const renderCopy = () => {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div
              className={styles.item}
              onClick={() => {
                handleCopy();
                // 上报埋点
                trackButtonClick('toolbar_copy_click');
              }}
            >
              {renderCopyIcon()}
            </div>
          </TooltipTrigger>
          <TooltipContent side="top" className="text-xs">
            {`Copy`}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  return (
    <div
      className={clsx(styles.container, {
        [styles.hide]: triggerHide,
      })}
      id="toolbar-panel"
    >
      {renderCopy()}

      {renderHighlight()}

      {renderAskAI()}

      {/* <div className={styles.line} /> */}

      {/* <QuickSnipButton type={type} /> */}
    </div>
  );
};

const getImageSrc = (element: Element): string | null => {
  if (element.tagName === 'IMG') {
    return element.getAttribute('src');
  } else if (element.tagName === 'PICTURE') {
    const img = element.querySelector('img');
    return img ? img.getAttribute('src') : null;
  }
  return null;
};
