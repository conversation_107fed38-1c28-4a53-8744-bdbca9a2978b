import { AssistantTypeEnum } from '@repo/common/types/assistant';
import { BoardItemTypeEnum } from '@repo/common/types/board-item';
import { produce } from 'immer';
import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
import { toolRegistry } from '@/components/mind-studio/tools';
import { sleep } from '@/utils/sleep';

interface MindWorkspaceState {
  currentMaterialId?: string;
  currentMaterialType?: BoardItemTypeEnum;
}

export const mindWorkspaceStateAtom = atom<MindWorkspaceState>({
  currentMaterialId: undefined,
  currentMaterialType: undefined,
});

type MindStudioBoardState = Record<
  string,
  {
    active: boolean;
    width: number | undefined;
  }
>;

// 简化的活动工具状态，直接存储 AssistantTypeEnum
export const mindStudioActiveToolAtom = atomWithStorage<AssistantTypeEnum | undefined>(
  'mindStudioActiveTool',
  AssistantTypeEnum.READER, // 默认选择 Reader
);

export const mindStudioBoardStateAtom = atomWithStorage<MindStudioBoardState>(
  'mindStudioBoardStateMap',
  {},
);

export const MIN_MIND_STUDIO_WIDTH = 320;
export const DEFAULT_MIND_STUDIO_WIDTH = 400;

export const getMindStudioWidthAtom = atom((get) => (boardId: string) => {
  const boardState = get(mindStudioBoardStateAtom);
  if (boardState[boardId]?.width && boardState[boardId]?.width > MIN_MIND_STUDIO_WIDTH) {
    return boardState[boardId]?.width;
  }
  return MIN_MIND_STUDIO_WIDTH;
});

export const setMindStudioWidthAtom = atom(null, (_get, set, boardId: string, width: number) => {
  set(
    mindStudioBoardStateAtom,
    produce((draft) => {
      if (draft[boardId]) {
        draft[boardId].width = width;
      } else {
        draft[boardId] = {
          active: true,
          width,
        };
      }
    }),
  );
});

export const mindStudioWidthDraggingAtom = atom(false);

export const openMindStudioIfNotActiveAtom = atom(
  null,
  async (get, set, boardId: string, sleepTime: number = 0) => {
    if (!get(mindStudioBoardStateAtom)[boardId]?.active) {
      set(setMindStudioActiveAtom, boardId, true);
      if (sleepTime) {
        await sleep(sleepTime);
      }
    }
  },
);

// 兼容性 atom - 返回当前活动工具的类型作为 ID
export const activeToolIdAtom = atom((get) => get(mindStudioActiveToolAtom));

// 读取当前活动工具的 atom - 从 toolRegistry 中查找
export const activeToolAtom = atom((get) => {
  const activeToolType = get(mindStudioActiveToolAtom);
  if (!activeToolType) return undefined;

  // 动态导入 toolRegistry 来避免循环依赖

  return toolRegistry.find((tool: { type: AssistantTypeEnum }) => tool.type === activeToolType);
});

// 设置 Note 面板为 active 状态
export const setNotePanelActiveAtom = atom(null, (_get, set) => {
  set(mindStudioActiveToolAtom, AssistantTypeEnum.NOTE);
});

// 设置 Chat 面板为 active 状态
export const setChatPanelActiveAtom = atom(null, (_get, set) => {
  set(mindStudioActiveToolAtom, AssistantTypeEnum.CHAT);
});

// 设置 Reader 面板为 active 状态
export const setReaderPanelActiveAtom = atom(null, (_get, set) => {
  set(mindStudioActiveToolAtom, AssistantTypeEnum.READER);
});

// 设置 Listen 面板为 active 状态
export const setListenPanelActiveAtom = atom(null, (_get, set) => {
  set(mindStudioActiveToolAtom, AssistantTypeEnum.LISTEN);
});

// 设置活动工具的 atom - 现在接受 AssistantTypeEnum
export const setActiveToolAtom = atom(null, (_get, set, toolType: AssistantTypeEnum) => {
  set(mindStudioActiveToolAtom, toolType);
});

export const resetActiveToolAtom = atom(null, (_get, set) => {
  set(mindStudioActiveToolAtom, AssistantTypeEnum.CHAT);
});

export const getMindStudioActiveAtom = atom((get) => (boardId: string) => {
  const boardState = get(mindStudioBoardStateAtom);

  if (!boardState[boardId]) {
    return false;
  }

  return boardState[boardId].active;
});

export const setMindStudioActiveAtom = atom(null, (_get, set, boardId: string, active: boolean) => {
  set(
    mindStudioBoardStateAtom,
    produce((draft) => {
      if (draft[boardId]) {
        draft[boardId].active = active;
      } else {
        draft[boardId] = {
          active,
          width: DEFAULT_MIND_STUDIO_WIDTH,
        };
      }
      // 如果激活了 Mind Studio，确保有一个默认的工具被选中
      if (active) {
        set(chooseFirstToolIfNeededAtom);
      }
    }),
  );
});

export const checkMindStudioAndOpenIfNeededAtom = atom(null, (get, set, boardId: string) => {
  const boardState = get(mindStudioBoardStateAtom);
  if (!boardState[boardId]) {
    set(setMindStudioActiveAtom, boardId, true);
  }
});

export const setMindStudioMaterialAtom = atom(
  null,
  (_get, set, materialType?: BoardItemTypeEnum, materialId?: string) => {
    set(
      mindWorkspaceStateAtom,
      produce((draft: MindWorkspaceState) => {
        draft.currentMaterialType = materialType;
        draft.currentMaterialId = materialId;
      }),
    );
  },
);

// 简化的工具选择逻辑 - 如果没有活动工具则选择第一个（Reader）
export const chooseFirstToolIfNeededAtom = atom(null, (get, set) => {
  const activeTool = get(mindStudioActiveToolAtom);
  if (!activeTool) {
    set(mindStudioActiveToolAtom, AssistantTypeEnum.CHAT);
  }
});

export const chooseFirstToolAtom = atom(null, (_get, set) => {
  set(mindStudioActiveToolAtom, AssistantTypeEnum.CHAT);
});
