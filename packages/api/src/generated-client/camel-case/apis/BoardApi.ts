/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import * as runtime from '../../../runtime';
import type {
  BoardDto,
  BoardIdDto,
  BoardWithCountDto,
  BoardWithItemsDto,
  BoardWithSomeBoardItemsDto,
  CreateBoardDto,
  DeleteBoardDto,
  GetBoardDto,
  ListBoardsDto,
  ListBoardsWithSomeBoardItemsDto,
  ListSimpleBoardsByEntityIdsDto,
  MoveEntityToUnsortedDto,
  SimpleBoardWithEntityIdsDto,
  UpdateBoardDto,
} from '../models/index';
import {
  BoardDtoFromJSON,
  BoardDtoToJSON,
  BoardIdDtoFromJSON,
  BoardIdDtoToJSON,
  BoardWithCountDtoFromJSON,
  BoardWithCountDtoToJSON,
  BoardWithItemsDtoFromJSON,
  BoardWithItemsDtoToJSON,
  BoardWithSomeBoardItemsDtoFromJSON,
  BoardWithSomeBoardItemsDtoToJSON,
  CreateBoardDtoFromJSON,
  CreateBoardDtoToJSON,
  DeleteBoardDtoFromJSON,
  DeleteBoardDtoToJSON,
  GetBoardDtoFromJSON,
  GetBoardDtoToJSON,
  ListBoardsDtoFromJSON,
  ListBoardsDtoToJSON,
  ListBoardsWithSomeBoardItemsDtoFromJSON,
  ListBoardsWithSomeBoardItemsDtoToJSON,
  ListSimpleBoardsByEntityIdsDtoFromJSON,
  ListSimpleBoardsByEntityIdsDtoToJSON,
  MoveEntityToUnsortedDtoFromJSON,
  MoveEntityToUnsortedDtoToJSON,
  SimpleBoardWithEntityIdsDtoFromJSON,
  SimpleBoardWithEntityIdsDtoToJSON,
  UpdateBoardDtoFromJSON,
  UpdateBoardDtoToJSON,
} from '../models/index';

export interface BoardControllerArchiveBoardRequest {
  boardIdDto: BoardIdDto;
}

export interface BoardControllerCreateBoardRequest {
  createBoardDto: CreateBoardDto;
}

export interface BoardControllerDeleteBoardRequest {
  deleteBoardDto: DeleteBoardDto;
}

export interface BoardControllerGetBoardRequest {
  getBoardDto: GetBoardDto;
}

export interface BoardControllerGetBoardDetailRequest {
  getBoardDto: GetBoardDto;
}

export interface BoardControllerListBoards0Request {
  listBoardsDto: ListBoardsDto;
}

export interface BoardControllerListBoards1Request {
  listBoardsDto: ListBoardsDto;
}

export interface BoardControllerListBoardsWithSomeBoardItemsRequest {
  listBoardsWithSomeBoardItemsDto: ListBoardsWithSomeBoardItemsDto;
}

export interface BoardControllerListSimpleBoardsByEntityIdsRequest {
  listSimpleBoardsByEntityIdsDto: ListSimpleBoardsByEntityIdsDto;
}

export interface BoardControllerMoveEntityToUnsortedRequest {
  moveEntityToUnsortedDto: MoveEntityToUnsortedDto;
}

export interface BoardControllerPatchBoardRequest {
  updateBoardDto: UpdateBoardDto;
}

export interface BoardControllerPinBoardRequest {
  boardIdDto: BoardIdDto;
}

export interface BoardControllerUnarchiveBoardRequest {
  boardIdDto: BoardIdDto;
}

export interface BoardControllerUnpinBoardRequest {
  boardIdDto: BoardIdDto;
}

/**
 * BoardApi - interface
 *
 * @export
 * @interface BoardApiInterface
 */
export interface BoardApiInterface {
  /**
   * 将看板状态设为归档
   * @summary 归档看板
   * @param {BoardIdDto} boardIdDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  archiveBoardRaw(
    requestParameters: BoardControllerArchiveBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>>;

  /**
   * 将看板状态设为归档
   * 归档看板
   */
  archiveBoard(
    boardIdDto: BoardIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto>;

  /**
   * 创建一个新的看板
   * @summary 创建看板
   * @param {CreateBoardDto} createBoardDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  createBoardRaw(
    requestParameters: BoardControllerCreateBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>>;

  /**
   * 创建一个新的看板
   * 创建看板
   */
  createBoard(
    createBoardDto: CreateBoardDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto>;

  /**
   * 根据模板创建新看板
   * @summary 从模板创建看板
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  createBoardFromTemplateRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>>;

  /**
   * 根据模板创建新看板
   * 从模板创建看板
   */
  createBoardFromTemplate(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto>;

  /**
   * 删除指定的看板
   * @summary 删除看板
   * @param {DeleteBoardDto} deleteBoardDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  deleteBoardRaw(
    requestParameters: BoardControllerDeleteBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 删除指定的看板
   * 删除看板
   */
  deleteBoard(
    deleteBoardDto: DeleteBoardDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * 获取指定看板的详细信息
   * @summary 获取看板详情
   * @param {GetBoardDto} getBoardDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  getBoardRaw(
    requestParameters: BoardControllerGetBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>>;

  /**
   * 获取指定看板的详细信息
   * 获取看板详情
   */
  getBoard(
    getBoardDto: GetBoardDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto>;

  /**
   * 获取看板详细信息，包括内容统计和看板项目
   * @summary 获取看板详细信息
   * @param {GetBoardDto} getBoardDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  getBoardDetailRaw(
    requestParameters: BoardControllerGetBoardDetailRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardWithItemsDto>>;

  /**
   * 获取看板详细信息，包括内容统计和看板项目
   * 获取看板详细信息
   */
  getBoardDetail(
    getBoardDto: GetBoardDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardWithItemsDto>;

  /**
   * 获取用户的默认看板
   * @summary 获取默认看板
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  getDefaultBoardRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>>;

  /**
   * 获取用户的默认看板
   * 获取默认看板
   */
  getDefaultBoard(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<BoardDto>;

  /**
   * 获取用户的看板列表，支持状态过滤和名称搜索
   * @summary 获取看板列表
   * @param {ListBoardsDto} listBoardsDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  listBoards0Raw(
    requestParameters: BoardControllerListBoards0Request,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<BoardWithCountDto>>>;

  /**
   * 获取用户的看板列表，支持状态过滤和名称搜索
   * 获取看板列表
   */
  listBoards0(
    listBoardsDto: ListBoardsDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<BoardWithCountDto>>;

  /**
   * 获取用户的看板列表，支持状态过滤和名称搜索
   * @summary 获取看板列表
   * @param {ListBoardsDto} listBoardsDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  listBoards1Raw(
    requestParameters: BoardControllerListBoards1Request,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<BoardWithCountDto>>>;

  /**
   * 获取用户的看板列表，支持状态过滤和名称搜索
   * 获取看板列表
   */
  listBoards1(
    listBoardsDto: ListBoardsDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<BoardWithCountDto>>;

  /**
   * 获取看板列表，包含部分看板项目
   * @summary 获取带有部分内容的看板列表
   * @param {ListBoardsWithSomeBoardItemsDto} listBoardsWithSomeBoardItemsDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  listBoardsWithSomeBoardItemsRaw(
    requestParameters: BoardControllerListBoardsWithSomeBoardItemsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<BoardWithSomeBoardItemsDto>>>;

  /**
   * 获取看板列表，包含部分看板项目
   * 获取带有部分内容的看板列表
   */
  listBoardsWithSomeBoardItems(
    listBoardsWithSomeBoardItemsDto: ListBoardsWithSomeBoardItemsDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<BoardWithSomeBoardItemsDto>>;

  /**
   * 根据 snip 和 thought ID 列表获取相关板块的简化信息（仅包含ID、名称、图标等部分字段）
   * @summary 根据实体ID列表获取简化板块信息
   * @param {ListSimpleBoardsByEntityIdsDto} listSimpleBoardsByEntityIdsDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  listSimpleBoardsByEntityIdsRaw(
    requestParameters: BoardControllerListSimpleBoardsByEntityIdsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<SimpleBoardWithEntityIdsDto>>>;

  /**
   * 根据 snip 和 thought ID 列表获取相关板块的简化信息（仅包含ID、名称、图标等部分字段）
   * 根据实体ID列表获取简化板块信息
   */
  listSimpleBoardsByEntityIds(
    listSimpleBoardsByEntityIdsDto: ListSimpleBoardsByEntityIdsDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<SimpleBoardWithEntityIdsDto>>;

  /**
   * 移动 snip 或 thought 到指定看板
   * @summary 移动实体到看板
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  moveEntityToBoardRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 移动 snip 或 thought 到指定看板
   * 移动实体到看板
   */
  moveEntityToBoard(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void>;

  /**
   * 移动 snip、thought 或其他实体到未分类（默认看板）
   * @summary 移动实体到未分类
   * @param {MoveEntityToUnsortedDto} moveEntityToUnsortedDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  moveEntityToUnsortedRaw(
    requestParameters: BoardControllerMoveEntityToUnsortedRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>>;

  /**
   * 移动 snip、thought 或其他实体到未分类（默认看板）
   * 移动实体到未分类
   */
  moveEntityToUnsorted(
    moveEntityToUnsortedDto: MoveEntityToUnsortedDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void>;

  /**
   * 更新看板的基本信息
   * @summary 更新看板
   * @param {UpdateBoardDto} updateBoardDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  patchBoardRaw(
    requestParameters: BoardControllerPatchBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>>;

  /**
   * 更新看板的基本信息
   * 更新看板
   */
  patchBoard(
    updateBoardDto: UpdateBoardDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto>;

  /**
   * 将看板置顶
   * @summary 置顶看板
   * @param {BoardIdDto} boardIdDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  pinBoardRaw(
    requestParameters: BoardControllerPinBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>>;

  /**
   * 将看板置顶
   * 置顶看板
   */
  pinBoard(
    boardIdDto: BoardIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto>;

  /**
   * 将看板状态从归档恢复为进行中
   * @summary 取消归档看板
   * @param {BoardIdDto} boardIdDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  unarchiveBoardRaw(
    requestParameters: BoardControllerUnarchiveBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>>;

  /**
   * 将看板状态从归档恢复为进行中
   * 取消归档看板
   */
  unarchiveBoard(
    boardIdDto: BoardIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto>;

  /**
   * 取消看板置顶状态
   * @summary 取消置顶看板
   * @param {BoardIdDto} boardIdDto
   * @param {*} [options] Override http request option.
   * @throws {RequiredError}
   * @memberof BoardApiInterface
   */
  unpinBoardRaw(
    requestParameters: BoardControllerUnpinBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>>;

  /**
   * 取消看板置顶状态
   * 取消置顶看板
   */
  unpinBoard(
    boardIdDto: BoardIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto>;
}

/**
 *
 */
export class BoardApi extends runtime.BaseAPI implements BoardApiInterface {
  /**
   * 将看板状态设为归档
   * 归档看板
   */
  async archiveBoardRaw(
    requestParameters: BoardControllerArchiveBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>> {
    if (requestParameters.boardIdDto == null) {
      throw new runtime.RequiredError(
        'boardIdDto',
        'Required parameter "boardIdDto" was null or undefined when calling archiveBoard().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/board/archiveBoard`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: BoardIdDtoToJSON(requestParameters.boardIdDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => BoardDtoFromJSON(jsonValue));
  }

  /**
   * 将看板状态设为归档
   * 归档看板
   */
  async archiveBoard(
    boardIdDto: BoardIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto> {
    const response = await this.archiveBoardRaw({ boardIdDto: boardIdDto }, initOverrides);
    return await response.value();
  }

  /**
   * 创建一个新的看板
   * 创建看板
   */
  async createBoardRaw(
    requestParameters: BoardControllerCreateBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>> {
    if (requestParameters.createBoardDto == null) {
      throw new runtime.RequiredError(
        'createBoardDto',
        'Required parameter "createBoardDto" was null or undefined when calling createBoard().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/createBoard`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: CreateBoardDtoToJSON(requestParameters.createBoardDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => BoardDtoFromJSON(jsonValue));
  }

  /**
   * 创建一个新的看板
   * 创建看板
   */
  async createBoard(
    createBoardDto: CreateBoardDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto> {
    const response = await this.createBoardRaw({ createBoardDto: createBoardDto }, initOverrides);
    return await response.value();
  }

  /**
   * 根据模板创建新看板
   * 从模板创建看板
   */
  async createBoardFromTemplateRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/createBoardFromTemplate`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => BoardDtoFromJSON(jsonValue));
  }

  /**
   * 根据模板创建新看板
   * 从模板创建看板
   */
  async createBoardFromTemplate(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto> {
    const response = await this.createBoardFromTemplateRaw(initOverrides);
    return await response.value();
  }

  /**
   * 删除指定的看板
   * 删除看板
   */
  async deleteBoardRaw(
    requestParameters: BoardControllerDeleteBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.deleteBoardDto == null) {
      throw new runtime.RequiredError(
        'deleteBoardDto',
        'Required parameter "deleteBoardDto" was null or undefined when calling deleteBoard().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/deleteBoard`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: DeleteBoardDtoToJSON(requestParameters.deleteBoardDto),
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 删除指定的看板
   * 删除看板
   */
  async deleteBoard(
    deleteBoardDto: DeleteBoardDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.deleteBoardRaw({ deleteBoardDto: deleteBoardDto }, initOverrides);
  }

  /**
   * 获取指定看板的详细信息
   * 获取看板详情
   */
  async getBoardRaw(
    requestParameters: BoardControllerGetBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>> {
    if (requestParameters.getBoardDto == null) {
      throw new runtime.RequiredError(
        'getBoardDto',
        'Required parameter "getBoardDto" was null or undefined when calling getBoard().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/board/getBoard`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: GetBoardDtoToJSON(requestParameters.getBoardDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => BoardDtoFromJSON(jsonValue));
  }

  /**
   * 获取指定看板的详细信息
   * 获取看板详情
   */
  async getBoard(
    getBoardDto: GetBoardDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto> {
    const response = await this.getBoardRaw({ getBoardDto: getBoardDto }, initOverrides);
    return await response.value();
  }

  /**
   * 获取看板详细信息，包括内容统计和看板项目
   * 获取看板详细信息
   */
  async getBoardDetailRaw(
    requestParameters: BoardControllerGetBoardDetailRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardWithItemsDto>> {
    if (requestParameters.getBoardDto == null) {
      throw new runtime.RequiredError(
        'getBoardDto',
        'Required parameter "getBoardDto" was null or undefined when calling getBoardDetail().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/board/getBoardDetail`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: GetBoardDtoToJSON(requestParameters.getBoardDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      BoardWithItemsDtoFromJSON(jsonValue),
    );
  }

  /**
   * 获取看板详细信息，包括内容统计和看板项目
   * 获取看板详细信息
   */
  async getBoardDetail(
    getBoardDto: GetBoardDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardWithItemsDto> {
    const response = await this.getBoardDetailRaw({ getBoardDto: getBoardDto }, initOverrides);
    return await response.value();
  }

  /**
   * 获取用户的默认看板
   * 获取默认看板
   */
  async getDefaultBoardRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/board/getDefaultBoard`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => BoardDtoFromJSON(jsonValue));
  }

  /**
   * 获取用户的默认看板
   * 获取默认看板
   */
  async getDefaultBoard(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto> {
    const response = await this.getDefaultBoardRaw(initOverrides);
    return await response.value();
  }

  /**
   * 获取用户的看板列表，支持状态过滤和名称搜索
   * 获取看板列表
   */
  async listBoards0Raw(
    requestParameters: BoardControllerListBoards0Request,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<BoardWithCountDto>>> {
    if (requestParameters.listBoardsDto == null) {
      throw new runtime.RequiredError(
        'listBoardsDto',
        'Required parameter "listBoardsDto" was null or undefined when calling listBoards0().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/listBoards`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: ListBoardsDtoToJSON(requestParameters.listBoardsDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      jsonValue.map(BoardWithCountDtoFromJSON),
    );
  }

  /**
   * 获取用户的看板列表，支持状态过滤和名称搜索
   * 获取看板列表
   */
  async listBoards0(
    listBoardsDto: ListBoardsDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<BoardWithCountDto>> {
    const response = await this.listBoards0Raw({ listBoardsDto: listBoardsDto }, initOverrides);
    return await response.value();
  }

  /**
   * 获取用户的看板列表，支持状态过滤和名称搜索
   * 获取看板列表
   */
  async listBoards1Raw(
    requestParameters: BoardControllerListBoards1Request,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<BoardWithCountDto>>> {
    if (requestParameters.listBoardsDto == null) {
      throw new runtime.RequiredError(
        'listBoardsDto',
        'Required parameter "listBoardsDto" was null or undefined when calling listBoards1().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/board/listBoards`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: ListBoardsDtoToJSON(requestParameters.listBoardsDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      jsonValue.map(BoardWithCountDtoFromJSON),
    );
  }

  /**
   * 获取用户的看板列表，支持状态过滤和名称搜索
   * 获取看板列表
   */
  async listBoards1(
    listBoardsDto: ListBoardsDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<BoardWithCountDto>> {
    const response = await this.listBoards1Raw({ listBoardsDto: listBoardsDto }, initOverrides);
    return await response.value();
  }

  /**
   * 获取看板列表，包含部分看板项目
   * 获取带有部分内容的看板列表
   */
  async listBoardsWithSomeBoardItemsRaw(
    requestParameters: BoardControllerListBoardsWithSomeBoardItemsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<BoardWithSomeBoardItemsDto>>> {
    if (requestParameters.listBoardsWithSomeBoardItemsDto == null) {
      throw new runtime.RequiredError(
        'listBoardsWithSomeBoardItemsDto',
        'Required parameter "listBoardsWithSomeBoardItemsDto" was null or undefined when calling listBoardsWithSomeBoardItems().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/board/listBoardsWithSomeBoardItems`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: ListBoardsWithSomeBoardItemsDtoToJSON(
          requestParameters.listBoardsWithSomeBoardItemsDto,
        ),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      jsonValue.map(BoardWithSomeBoardItemsDtoFromJSON),
    );
  }

  /**
   * 获取看板列表，包含部分看板项目
   * 获取带有部分内容的看板列表
   */
  async listBoardsWithSomeBoardItems(
    listBoardsWithSomeBoardItemsDto: ListBoardsWithSomeBoardItemsDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<BoardWithSomeBoardItemsDto>> {
    const response = await this.listBoardsWithSomeBoardItemsRaw(
      { listBoardsWithSomeBoardItemsDto: listBoardsWithSomeBoardItemsDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * 根据 snip 和 thought ID 列表获取相关板块的简化信息（仅包含ID、名称、图标等部分字段）
   * 根据实体ID列表获取简化板块信息
   */
  async listSimpleBoardsByEntityIdsRaw(
    requestParameters: BoardControllerListSimpleBoardsByEntityIdsRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<Array<SimpleBoardWithEntityIdsDto>>> {
    if (requestParameters.listSimpleBoardsByEntityIdsDto == null) {
      throw new runtime.RequiredError(
        'listSimpleBoardsByEntityIdsDto',
        'Required parameter "listSimpleBoardsByEntityIdsDto" was null or undefined when calling listSimpleBoardsByEntityIds().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/listSimpleBoardsByEntityIds`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: ListSimpleBoardsByEntityIdsDtoToJSON(
          requestParameters.listSimpleBoardsByEntityIdsDto,
        ),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) =>
      jsonValue.map(SimpleBoardWithEntityIdsDtoFromJSON),
    );
  }

  /**
   * 根据 snip 和 thought ID 列表获取相关板块的简化信息（仅包含ID、名称、图标等部分字段）
   * 根据实体ID列表获取简化板块信息
   */
  async listSimpleBoardsByEntityIds(
    listSimpleBoardsByEntityIdsDto: ListSimpleBoardsByEntityIdsDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<Array<SimpleBoardWithEntityIdsDto>> {
    const response = await this.listSimpleBoardsByEntityIdsRaw(
      { listSimpleBoardsByEntityIdsDto: listSimpleBoardsByEntityIdsDto },
      initOverrides,
    );
    return await response.value();
  }

  /**
   * 移动 snip 或 thought 到指定看板
   * 移动实体到看板
   */
  async moveEntityToBoardRaw(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    const urlPath = `/api/v1/moveEntityToBoard`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 移动 snip 或 thought 到指定看板
   * 移动实体到看板
   */
  async moveEntityToBoard(
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.moveEntityToBoardRaw(initOverrides);
  }

  /**
   * 移动 snip、thought 或其他实体到未分类（默认看板）
   * 移动实体到未分类
   */
  async moveEntityToUnsortedRaw(
    requestParameters: BoardControllerMoveEntityToUnsortedRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<void>> {
    if (requestParameters.moveEntityToUnsortedDto == null) {
      throw new runtime.RequiredError(
        'moveEntityToUnsortedDto',
        'Required parameter "moveEntityToUnsortedDto" was null or undefined when calling moveEntityToUnsorted().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/moveEntityToUnsorted`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: MoveEntityToUnsortedDtoToJSON(requestParameters.moveEntityToUnsortedDto),
      },
      initOverrides,
    );

    return new runtime.VoidApiResponse(response);
  }

  /**
   * 移动 snip、thought 或其他实体到未分类（默认看板）
   * 移动实体到未分类
   */
  async moveEntityToUnsorted(
    moveEntityToUnsortedDto: MoveEntityToUnsortedDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<void> {
    await this.moveEntityToUnsortedRaw(
      { moveEntityToUnsortedDto: moveEntityToUnsortedDto },
      initOverrides,
    );
  }

  /**
   * 更新看板的基本信息
   * 更新看板
   */
  async patchBoardRaw(
    requestParameters: BoardControllerPatchBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>> {
    if (requestParameters.updateBoardDto == null) {
      throw new runtime.RequiredError(
        'updateBoardDto',
        'Required parameter "updateBoardDto" was null or undefined when calling patchBoard().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/board/patchBoard`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: UpdateBoardDtoToJSON(requestParameters.updateBoardDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => BoardDtoFromJSON(jsonValue));
  }

  /**
   * 更新看板的基本信息
   * 更新看板
   */
  async patchBoard(
    updateBoardDto: UpdateBoardDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto> {
    const response = await this.patchBoardRaw({ updateBoardDto: updateBoardDto }, initOverrides);
    return await response.value();
  }

  /**
   * 将看板置顶
   * 置顶看板
   */
  async pinBoardRaw(
    requestParameters: BoardControllerPinBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>> {
    if (requestParameters.boardIdDto == null) {
      throw new runtime.RequiredError(
        'boardIdDto',
        'Required parameter "boardIdDto" was null or undefined when calling pinBoard().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/board/pinBoard`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: BoardIdDtoToJSON(requestParameters.boardIdDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => BoardDtoFromJSON(jsonValue));
  }

  /**
   * 将看板置顶
   * 置顶看板
   */
  async pinBoard(
    boardIdDto: BoardIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto> {
    const response = await this.pinBoardRaw({ boardIdDto: boardIdDto }, initOverrides);
    return await response.value();
  }

  /**
   * 将看板状态从归档恢复为进行中
   * 取消归档看板
   */
  async unarchiveBoardRaw(
    requestParameters: BoardControllerUnarchiveBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>> {
    if (requestParameters.boardIdDto == null) {
      throw new runtime.RequiredError(
        'boardIdDto',
        'Required parameter "boardIdDto" was null or undefined when calling unarchiveBoard().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/board/unarchiveBoard`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: BoardIdDtoToJSON(requestParameters.boardIdDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => BoardDtoFromJSON(jsonValue));
  }

  /**
   * 将看板状态从归档恢复为进行中
   * 取消归档看板
   */
  async unarchiveBoard(
    boardIdDto: BoardIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto> {
    const response = await this.unarchiveBoardRaw({ boardIdDto: boardIdDto }, initOverrides);
    return await response.value();
  }

  /**
   * 取消看板置顶状态
   * 取消置顶看板
   */
  async unpinBoardRaw(
    requestParameters: BoardControllerUnpinBoardRequest,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<runtime.ApiResponse<BoardDto>> {
    if (requestParameters.boardIdDto == null) {
      throw new runtime.RequiredError(
        'boardIdDto',
        'Required parameter "boardIdDto" was null or undefined when calling unpinBoard().',
      );
    }

    const queryParameters: any = {};

    const headerParameters: runtime.HTTPHeaders = {};

    headerParameters['Content-Type'] = 'application/json';

    const urlPath = `/api/v1/board/unpinBoard`;

    const response = await this.request(
      {
        path: urlPath,
        method: 'POST',
        headers: headerParameters,
        query: queryParameters,
        body: BoardIdDtoToJSON(requestParameters.boardIdDto),
      },
      initOverrides,
    );

    return new runtime.JSONApiResponse(response, (jsonValue) => BoardDtoFromJSON(jsonValue));
  }

  /**
   * 取消看板置顶状态
   * 取消置顶看板
   */
  async unpinBoard(
    boardIdDto: BoardIdDto,
    initOverrides?: RequestInit | runtime.InitOverrideFunction,
  ): Promise<BoardDto> {
    const response = await this.unpinBoardRaw({ boardIdDto: boardIdDto }, initOverrides);
    return await response.value();
  }
}
