/* tslint:disable */
/* eslint-disable */
/**
 * YouAPI
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../../../runtime';
/**
 *
 * @export
 * @interface ListBoardsWithSomeBoardItemsDto
 */
export interface ListBoardsWithSomeBoardItemsDto {
  /**
   * 名称模糊搜索
   * @type {string}
   * @memberof ListBoardsWithSomeBoardItemsDto
   */
  fuzzyName?: string;
  /**
   * 看板状态
   * @type {string}
   * @memberof ListBoardsWithSomeBoardItemsDto
   */
  status?: ListBoardsWithSomeBoardItemsDtoStatusEnum;
}

/**
 * @export
 * @enum {string}
 */
export enum ListBoardsWithSomeBoardItemsDtoStatusEnum {
  inProgress = 'in-progress',
  other = 'other',
  unknownDefaultOpenApi = '11184809',
}

/**
 * Check if a given object implements the ListBoardsWithSomeBoardItemsDto interface.
 */
export function instanceOfListBoardsWithSomeBoardItemsDto(
  value: object,
): value is ListBoardsWithSomeBoardItemsDto {
  return true;
}

export function ListBoardsWithSomeBoardItemsDtoFromJSON(
  json: any,
): ListBoardsWithSomeBoardItemsDto {
  return ListBoardsWithSomeBoardItemsDtoFromJSONTyped(json, false);
}

export function ListBoardsWithSomeBoardItemsDtoFromJSONTyped(
  json: any,
  _ignoreDiscriminator: boolean,
): ListBoardsWithSomeBoardItemsDto {
  if (json == null) {
    return json;
  }
  return {
    fuzzyName: json.fuzzyName == null ? undefined : json.fuzzyName,
    status: json.status == null ? undefined : json.status,
  };
}

export function ListBoardsWithSomeBoardItemsDtoToJSON(json: any): ListBoardsWithSomeBoardItemsDto {
  return ListBoardsWithSomeBoardItemsDtoToJSONTyped(json, false);
}

export function ListBoardsWithSomeBoardItemsDtoToJSONTyped(
  value?: ListBoardsWithSomeBoardItemsDto | null,
  _ignoreDiscriminator: boolean = false,
): any {
  if (value == null) {
    return value;
  }

  return {
    fuzzyName: value.fuzzyName,
    status: value.status,
  };
}
