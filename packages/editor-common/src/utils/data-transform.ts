import { generateJSO<PERSON>, type <PERSON><PERSON>NContent } from '@tiptap/core';
import { type Fragment, Node } from '@tiptap/pm/model';
import { prosemirrorJSONToYXmlFragment, yXmlFragmentToProseMirrorRootNode } from 'y-prosemirror';
import { applyUpdate, Doc, encodeStateAsUpdate, transact } from 'yjs';
import { DOC_FRAGMENT_FIELED } from '../const';
import { EditorSchema, SchemaExtension } from '../extensions';

export function base64ToUint8Array(base64: string): Uint8Array {
  const binaryString = atob(base64);
  const len = binaryString.length;
  const uint8Array = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    uint8Array[i] = binaryString.charCodeAt(i);
  }
  return uint8Array;
}

export function uint8ArrayToBase64(binary: Uint8Array): string {
  const CHUNK_SIZE = 8192;
  let result = '';

  for (let i = 0; i < binary.length; i += CHUNK_SIZE) {
    const chunk = binary.subarray(i, i + CHUNK_SIZE);
    result += String.fromCharCode.apply(null, Array.from(chunk));
  }

  return btoa(result);
}

export function encodeDocToBase64(doc: Doc): string {
  const uint8Array = encodeStateAsUpdate(doc);
  return uint8ArrayToBase64(uint8Array);
}

export function convertBase64ToProseMirrorNode(base64: string): Node {
  const doc = new Doc();
  const uint8Array = base64ToUint8Array(base64);

  try {
    // @see https://discuss.yjs.dev/t/react-ydoc-uncaught-error-unexpected-end-of-array/1724
    // 应用更新到 doc
    applyUpdate(doc, uint8Array);
  } catch (error) {
    console.error(error);
  }

  // 获取 XmlFragment
  const xmlFragment = doc.getXmlFragment(DOC_FRAGMENT_FIELED);

  // 将 XmlFragment 转换为 Prosemirror Node
  const prosemirrorNode = yXmlFragmentToProseMirrorRootNode(xmlFragment, EditorSchema);

  return prosemirrorNode;
}

export const createDocByBase64 = (base64: string): Doc => {
  if (!base64) {
    return new Doc();
  }
  const doc = new Doc();
  const uint8Array = base64ToUint8Array(base64);
  transact(
    doc,
    () => {
      applyUpdate(doc, uint8Array);
    },
    DOC_FRAGMENT_FIELED,
    false,
  );
  return doc;
};

export const contentJSONToBase64 = (json: JSONContent) => {
  const doc = new Doc();
  const fragment = doc.getXmlFragment(DOC_FRAGMENT_FIELED);
  prosemirrorJSONToYXmlFragment(EditorSchema, json, fragment);
  return encodeDocToBase64(doc);
};

export const htmlToBase64 = (html: string) => {
  const json = generateJSON(html, SchemaExtension);
  return contentJSONToBase64(json);
};

export const createDocByJSONContent = (json: JSONContent) => {
  const doc = new Doc();
  const base64 = contentJSONToBase64(json);
  const uint8Array = base64ToUint8Array(base64);
  transact(
    doc,
    () => {
      applyUpdate(doc, uint8Array);
    },
    DOC_FRAGMENT_FIELED,
    false,
  );
  return doc;
};

export const pmFragmentToNode = (fragment: Fragment) => {
  return Node.fromJSON(EditorSchema, {
    type: 'doc',
    content: fragment.toJSON(),
  });
};

export const base64ToJSON = (base64: string): JSONContent => {
  const node = convertBase64ToProseMirrorNode(base64);
  return node.toJSON();
};

const PARAGRAPH_TYPES = ['paragraph', 'heading'];
export function extractPlainText(json: JSONContent): string {
  if (!json.content) {
    return '';
  }

  const texts: string[] = [];

  function traverse(node: JSONContent) {
    if (PARAGRAPH_TYPES.includes(node.type || '')) {
      const text =
        node.content?.map((child) => (child.type === 'text' ? child.text : '')).join('') || '';
      texts.push(text);
    }

    if (node.content) {
      for (const child of node.content) {
        traverse(child);
      }
    }
  }

  for (const node of json.content) {
    traverse(node);
  }

  return texts.join('\n');
}
